<?php
// تعريف الوصول الآمن
define('SECURE_ACCESS', true);

// بدء الجلسة
session_start();

// تضمين ملفات النظام
require_once 'includes/error_handler.php';
require_once 'config/db.php';
require_once 'includes/security.php';
require_once 'includes/functions.php';

// التحقق من وضع الصيانة
$maintenance_check = $conn->query("SELECT setting_value FROM settings WHERE setting_key = 'maintenance_mode'");
if ($maintenance_check && $maintenance_check->num_rows > 0) {
    $maintenance = $maintenance_check->fetch_assoc();
    if ($maintenance['setting_value'] == '1') {
        // التحقق من أن المستخدم ليس مديراً
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'الإدارة العامة') {
            include 'maintenance.php';
            exit;
        }
    }
}

// التحقق من وجود جلسة مستخدم نشطة
if (isset($_SESSION['user'])) {
    // توجيه المستخدم إلى الصفحة المناسبة بناءً على دوره
    $role = $_SESSION['user']['role'];
    switch ($role) {
        case 'الإدارة العامة':
            header("Location: dashboard/index.php");
            exit();
        case 'مدير':
            header("Location: admin/index.php");
            exit();
        case 'إدارة الجوال':
            header("Location: mobile/index.php");
            exit();
        case 'المخابر':
            header("Location: lab/index.php");
            exit();
        case 'الهندسة':
            header("Location: engineering/index.php");
            exit();
        case 'الصيانة':
            header("Location: maintenance/index.php");
            exit();
    }
}

// الحصول على إعدادات الموقع
$site_settings = [];
$settings_query = $conn->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('site_name', 'site_description')");
if ($settings_query) {
    while ($row = $settings_query->fetch_assoc()) {
        $site_settings[$row['setting_key']] = $row['setting_value'];
    }
}

$site_name = $site_settings['site_name'] ?? 'الرصين';
$site_description = $site_settings['site_description'] ?? 'منصة تنظيم التواصل بين وحدات قسم الإتصالات';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($site_name); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($site_description); ?>">
    <meta name="keywords" content="الرصين, إتصالات, إدارة, تنظيم">
    <meta name="author" content="فريق تطوير الرصين">
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet" integrity="sha384-dpuaG1suU0eT09tx5plTaGMLBsfDLzUCCUXOY2j/LSvXYuG6Bqs43ALlhIqAJVRb" crossorigin="anonymous">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/favicon.png">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #0ea5e9;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }
        
        body {
            min-height: 100vh;
            margin: 0;
            padding: 0;
            background: linear-gradient(120deg, var(--light-color) 0%, #e0e7ff 100%);
            font-family: 'Tajawal', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }
        
        /* خلفية متحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="%23ffffff" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            z-index: -1;
        }
        
        .main-container {
            position: relative;
            z-index: 1;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 22px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.13);
            padding: 3rem 2.5rem 2.5rem 2.5rem;
            max-width: 420px;
            width: 100%;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo-circle {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            box-shadow: 0 4px 18px rgba(99,102,241,0.13);
            position: relative;
            overflow: hidden;
        }
        
        .logo-circle::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }
        
        .logo-circle img {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #fff;
            padding: 10px;
            position: relative;
            z-index: 2;
        }
        
        .logo-circle .logo-text {
            font-size: 2.5rem;
            color: white;
            font-weight: 700;
            position: relative;
            z-index: 2;
        }
        
        .main-card h1 {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.7rem;
            font-size: 2.3rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .main-card .lead-text {
            color: #64748b;
            font-size: 1.1rem;
            margin-bottom: 2.2rem;
            line-height: 1.6;
        }
        
        .btn-custom {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: #fff;
            font-weight: bold;
            border: none;
            border-radius: 30px;
            padding: 13px 0;
            width: 100%;
            font-size: 1.1rem;
            box-shadow: 0 2px 10px rgba(99,102,241,0.09);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-custom:hover {
            background: linear-gradient(90deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 4px 20px rgba(99,102,241,0.2);
            color: #fff;
        }
        
        .btn-custom:hover::before {
            left: 100%;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature-item {
            background: rgba(99, 102, 241, 0.1);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .feature-item:hover {
            transform: translateY(-2px);
        }
        
        .feature-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-text {
            font-size: 0.9rem;
            color: var(--dark-color);
            font-weight: 500;
        }
        
        footer {
            margin-top: 40px;
            font-size: 0.95rem;
            color: #888;
            text-align: center;
        }
        
        .system-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: var(--success-color);
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .main-card {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
            
            .logo-circle {
                width: 100px;
                height: 100px;
            }
            
            .logo-circle img,
            .logo-circle .logo-text {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }
            
            .main-card h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-card mx-auto">
            <div class="system-status">
                <div class="status-indicator"></div>
                <span>النظام يعمل بشكل طبيعي</span>
            </div>
            
            <div class="logo-circle">
                <?php if (file_exists('assets/images/logo.png')): ?>
                    <img src="assets/images/logo.png" alt="شعار <?php echo htmlspecialchars($site_name); ?>">
                <?php else: ?>
                    <div class="logo-text">📡</div>
                <?php endif; ?>
            </div>
            
            <h1><?php echo htmlspecialchars($site_name); ?></h1>
            <div class="lead-text mb-4"><?php echo htmlspecialchars($site_description); ?></div>
            
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-text">نظام آمن</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-text">سريع وموثوق</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-text">متوافق مع الجوال</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🌐</div>
                    <div class="feature-text">واجهة عربية</div>
                </div>
            </div>
            
            <a href="login.php" class="btn btn-custom mb-2">الدخول إلى النظام</a>
        </div>
        
        <footer class="pt-4">
            &copy; <?php echo date("Y"); ?> <?php echo htmlspecialchars($site_name); ?> - جميع الحقوق محفوظة
            <br>
            <small class="text-muted">الإصدار 2.0 - محدث ومحسن</small>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    
    <!-- تحسينات الأداء -->
    <script>
        // تحسين تحميل الصور
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.loading = 'lazy';
            });
        });
        
        // إضافة تأثيرات تفاعلية
        document.querySelector('.btn-custom').addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.03)';
        });
        
        document.querySelector('.btn-custom').addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    </script>
</body>
</html>
