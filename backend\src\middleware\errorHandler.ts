import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger, loggerHelpers } from '@/utils/logger';
import { config } from '@/config/config';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

/**
 * Custom error class for API errors
 */
export class AppError extends Error implements ApiError {
  public statusCode: number;
  public code: string;
  public details?: any;
  public isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error class
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * Not found error class
 */
export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
  }
}

/**
 * Unauthorized error class
 */
export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

/**
 * Forbidden error class
 */
export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
  }
}

/**
 * Conflict error class
 */
export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT', details);
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

/**
 * Handle Prisma errors
 */
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): AppError => {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[] | undefined;
      const fieldName = field ? field[0] : 'field';
      return new ConflictError(
        `A record with this ${fieldName} already exists`,
        { field: fieldName, value: error.meta?.['target'] }
      );

    case 'P2025':
      // Record not found
      return new NotFoundError('Record');

    case 'P2003':
      // Foreign key constraint violation
      return new ValidationError(
        'Invalid reference to related record',
        { field: error.meta?.['field_name'] }
      );

    case 'P2014':
      // Required relation violation
      return new ValidationError(
        'Required relation is missing',
        { relation: error.meta?.['relation_name'] }
      );

    case 'P2021':
      // Table does not exist
      return new AppError(
        'Database table does not exist',
        500,
        'DATABASE_ERROR',
        { table: error.meta?.['table'] }
      );

    case 'P2022':
      // Column does not exist
      return new AppError(
        'Database column does not exist',
        500,
        'DATABASE_ERROR',
        { column: error.meta?.['column'] }
      );

    default:
      return new AppError(
        'Database operation failed',
        500,
        'DATABASE_ERROR',
        { prismaCode: error.code, meta: error.meta }
      );
  }
};

/**
 * Handle JWT errors
 */
const handleJWTError = (error: Error): AppError => {
  if (error.name === 'JsonWebTokenError') {
    return new UnauthorizedError('Invalid token');
  }
  
  if (error.name === 'TokenExpiredError') {
    return new UnauthorizedError('Token expired');
  }
  
  if (error.name === 'NotBeforeError') {
    return new UnauthorizedError('Token not active');
  }

  return new UnauthorizedError('Token verification failed');
};

/**
 * Handle validation errors
 */
const handleValidationError = (error: any): AppError => {
  if (error.isJoi) {
    // Joi validation error
    const details = error.details.map((detail: any) => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value,
    }));

    return new ValidationError(
      'Validation failed',
      { errors: details }
    );
  }

  return new ValidationError(error.message);
};

/**
 * Send error response
 */
const sendErrorResponse = (error: ApiError, req: Request, res: Response): void => {
  const statusCode = error.statusCode || 500;
  const isDevelopment = config.nodeEnv === 'development';

  // Base error response
  const errorResponse: any = {
    error: error.message,
    code: error.code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
  };

  // Add details in development or for client errors
  if (error.details && (isDevelopment || statusCode < 500)) {
    errorResponse.details = error.details;
  }

  // Add stack trace in development
  if (isDevelopment && error.stack) {
    errorResponse.stack = error.stack;
  }

  // Add request ID if available
  if (req.headers['x-request-id']) {
    errorResponse.requestId = req.headers['x-request-id'];
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * Global error handler middleware
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let apiError: ApiError;

  // Handle known error types
  if (error instanceof AppError) {
    apiError = error;
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    apiError = handlePrismaError(error);
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    apiError = new ValidationError('Invalid data provided to database');
  } else if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    apiError = new AppError('Database request failed', 500, 'DATABASE_ERROR');
  } else if (error.name?.includes('JWT') || error.name?.includes('Token')) {
    apiError = handleJWTError(error);
  } else if (error.name === 'ValidationError' || (error as any).isJoi) {
    apiError = handleValidationError(error);
  } else if (error.name === 'CastError') {
    apiError = new ValidationError('Invalid ID format');
  } else if (error.name === 'MulterError') {
    apiError = new ValidationError(`File upload error: ${error.message}`);
  } else {
    // Unknown error
    apiError = new AppError(
      config.nodeEnv === 'development' ? error.message : 'Internal server error',
      500,
      'INTERNAL_ERROR'
    );
  }

  // Log error
  const logLevel = (apiError.statusCode || 500) >= 500 ? 'error' : 'warn';
  const logContext = {
    statusCode: apiError.statusCode,
    code: apiError.code,
    path: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    details: apiError.details,
  };

  if (logLevel === 'error') {
    loggerHelpers.logError(error, 'API Error Handler', logContext);
  } else {
    logger.warn(`API Warning: ${apiError.message}`, logContext);
  }

  // Send error response
  sendErrorResponse(apiError, req, res);
};

/**
 * Async error wrapper
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 handler for undefined routes
 */
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Validation middleware using Joi
 */
export const validate = (schema: any, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
    });

    if (error) {
      next(new ValidationError('Validation failed', {
        errors: error.details.map((detail: any) => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value,
        })),
      }));
      return;
    }

    next();
  };
};
