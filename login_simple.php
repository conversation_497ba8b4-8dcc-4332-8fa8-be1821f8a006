<?php
session_start();

// إذا كان المستخدم مسجل دخول، توجيهه للصفحة المناسبة
if (isset($_SESSION['user'])) {
    header("Location: dashboard/index.php");
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // محاولة الاتصال بقاعدة البيانات
        try {
            require_once 'config/db.php';
            
            if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
                // التحقق من المستخدم في قاعدة البيانات
                $stmt = $conn->prepare("SELECT id, username, password, role, status FROM users WHERE username = ? AND status = 'نشط'");
                $stmt->bind_param("s", $username);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $user = $result->fetch_assoc();
                    
                    if (password_verify($password, $user['password'])) {
                        // تسجيل دخول ناجح
                        $_SESSION['user'] = [
                            'id' => $user['id'],
                            'username' => $user['username'],
                            'role' => $user['role']
                        ];
                        
                        // توجيه حسب الدور
                        switch ($user['role']) {
                            case 'مدير':
                                header("Location: admin/index.php");
                                break;
                            case 'هندسة':
                                header("Location: engineering/index.php");
                                break;
                            case 'صيانة':
                                header("Location: maintenance/index.php");
                                break;
                            case 'مختبر':
                                header("Location: lab/index.php");
                                break;
                            default:
                                header("Location: dashboard/index.php");
                        }
                        exit();
                    } else {
                        $error_message = 'كلمة المرور غير صحيحة';
                    }
                } else {
                    $error_message = 'اسم المستخدم غير موجود أو الحساب غير نشط';
                }
            } else {
                // استخدام بيانات تجريبية إذا لم تكن قاعدة البيانات متاحة
                $demo_users = [
                    'admin' => ['password' => 'admin123', 'role' => 'مدير'],
                    'engineering' => ['password' => 'eng123', 'role' => 'هندسة'],
                    'maintenance' => ['password' => 'maint123', 'role' => 'صيانة'],
                    'lab' => ['password' => 'lab123', 'role' => 'مختبر']
                ];
                
                if (isset($demo_users[$username]) && $demo_users[$username]['password'] === $password) {
                    $_SESSION['user'] = [
                        'id' => 1,
                        'username' => $username,
                        'role' => $demo_users[$username]['role']
                    ];
                    
                    $success_message = 'تم تسجيل الدخول بنجاح! (وضع تجريبي)';
                    // إعادة توجيه بعد ثانيتين
                    header("refresh:2;url=dashboard/index.php");
                } else {
                    $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
            error_log("خطأ في تسجيل الدخول: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - الرصين</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(120deg, #f8fafc 0%, #e0e7ff 100%);
            font-family: 'Tajawal', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 22px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.13);
            padding: 3rem 2.5rem;
            max-width: 400px;
            width: 100%;
        }
        
        .logo-circle {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #6366f1 0%, #0ea5e9 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            font-size: 2rem;
            color: white;
        }
        
        .btn-login {
            background: linear-gradient(90deg, #6366f1 0%, #0ea5e9 100%);
            color: #fff;
            font-weight: bold;
            border: none;
            border-radius: 30px;
            padding: 13px 0;
            width: 100%;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(99,102,241,0.2);
            color: #fff;
        }
        
        .form-control {
            border-radius: 15px;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }
        
        .demo-info {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="text-center">
            <div class="logo-circle">🔐</div>
            <h2 class="mb-4">تسجيل الدخول</h2>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="demo-info">
            <strong>بيانات تجريبية:</strong><br>
            • admin / admin123 (مدير)<br>
            • engineering / eng123 (هندسة)<br>
            • maintenance / maint123 (صيانة)<br>
            • lab / lab123 (مختبر)
        </div>
        
        <form method="POST" action="">
            <div class="mb-3">
                <label for="username" class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
            </div>
            
            <div class="mb-4">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn btn-login mb-3">دخول</button>
        </form>
        
        <div class="text-center">
            <a href="index.php" class="text-decoration-none">← العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
