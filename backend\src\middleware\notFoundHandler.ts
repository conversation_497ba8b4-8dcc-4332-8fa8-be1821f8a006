import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

/**
 * 404 Not Found handler middleware
 */
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction): void => {
  // Log the 404 request
  logger.warn('404 Not Found', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
  });

  // Send 404 response
  res.status(404).json({
    error: 'Not Found',
    message: `The requested resource '${req.originalUrl}' was not found on this server.`,
    code: 'NOT_FOUND',
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    suggestions: getSuggestions(req.originalUrl),
  });
};

/**
 * Get suggestions for similar routes
 */
function getSuggestions(path: string): string[] {
  const suggestions: string[] = [];
  
  // Common API endpoints
  const commonEndpoints = [
    '/api/v1/auth/login',
    '/api/v1/auth/register',
    '/api/v1/devices',
    '/api/v1/alerts',
    '/api/v1/users',
    '/api/v1/dashboard',
    '/api/v1/settings',
    '/health',
  ];

  // Find similar paths
  const pathLower = path.toLowerCase();
  
  for (const endpoint of commonEndpoints) {
    const endpointLower = endpoint.toLowerCase();
    
    // Check if the path contains parts of the endpoint
    if (pathLower.includes(endpointLower.split('/').pop() || '') ||
        endpointLower.includes(pathLower.split('/').pop() || '')) {
      suggestions.push(endpoint);
    }
  }

  // Remove duplicates and limit to 3 suggestions
  return [...new Set(suggestions)].slice(0, 3);
}

export default notFoundHandler;
