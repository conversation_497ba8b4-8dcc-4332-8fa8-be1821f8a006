@echo off
setlocal enabledelayedexpansion

REM MikroTik Monitoring System - Quick Start Script for Windows
REM نص تشغيل سريع لنظام مراقبة المايكروتك - ويندوز

echo.
echo 🚀 MikroTik Monitoring System - Quick Start
echo ==========================================
echo.

REM Function to check if command exists
where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop first.
    echo Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

where docker-compose >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose first.
    echo Visit: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)

echo [INFO] Docker and Docker Compose are installed
echo.

REM Setup environment files
echo [INFO] Setting up environment files...

if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        echo [SUCCESS] Created backend\.env from example
    ) else (
        echo [WARNING] backend\.env.example not found
    )
) else (
    echo [INFO] backend\.env already exists
)

if not exist "frontend\.env.local" (
    echo [SUCCESS] frontend\.env.local already created
) else (
    echo [INFO] frontend\.env.local already exists
)

echo.
echo Choose installation method:
echo 1) Docker (Recommended - Easy setup)
echo 2) Manual (Development - Requires Node.js)
echo 3) Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto docker_start
if "%choice%"=="2" goto manual_start
if "%choice%"=="3" goto exit_script
echo [ERROR] Invalid choice. Please run the script again.
pause
exit /b 1

:docker_start
echo.
echo [INFO] Starting MikroTik Monitoring System with Docker...

REM Check if docker-compose.yml exists
if not exist "docker-compose.yml" (
    echo [ERROR] docker-compose.yml not found in current directory
    pause
    exit /b 1
)

REM Stop any running containers
echo [INFO] Stopping any existing containers...
docker-compose down 2>nul

REM Start services
echo [INFO] Starting services...
docker-compose up -d

if %errorlevel% neq 0 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)

REM Wait for services to be ready
echo [INFO] Waiting for services to start...
timeout /t 15 /nobreak >nul

REM Check service health
echo [INFO] Checking service health...

REM Check backend health (simple check)
for /l %%i in (1,1,30) do (
    curl -f http://localhost:3001/health >nul 2>nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] Backend is healthy
        goto check_frontend
    )
    timeout /t 2 /nobreak >nul
)
echo [ERROR] Backend health check failed
docker-compose logs backend
pause
exit /b 1

:check_frontend
REM Check frontend (simple check)
for /l %%i in (1,1,30) do (
    curl -f http://localhost:3000 >nul 2>nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] Frontend is healthy
        goto success
    )
    timeout /t 2 /nobreak >nul
)
echo [ERROR] Frontend health check failed
docker-compose logs frontend
pause
exit /b 1

:success
echo.
echo [SUCCESS] All services are running!
echo.
echo 🎉 MikroTik Monitoring System is ready!
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:3001
echo 📊 Health Check: http://localhost:3001/health
echo.
echo 🔑 Default Login:
echo    Email: <EMAIL>
echo    Password: admin123
echo.
echo 📝 View logs: docker-compose logs -f
echo 🛑 Stop system: docker-compose down
echo.
echo Opening browser...
start http://localhost:3000
echo.
echo Press any key to view logs, or Ctrl+C to exit...
pause >nul
docker-compose logs -f
goto :eof

:manual_start
echo.
echo [INFO] Starting MikroTik Monitoring System manually...

REM Check Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ first.
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

REM Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found in current directory
    pause
    exit /b 1
)

echo [INFO] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

echo [INFO] Installing all project dependencies...
call npm run install:all
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install project dependencies
    pause
    exit /b 1
)

echo [INFO] Setting up backend database...
call npm run setup:backend
if %errorlevel% neq 0 (
    echo [ERROR] Failed to setup backend
    pause
    exit /b 1
)

echo.
echo [INFO] Starting development servers...
echo [WARNING] This will start both backend and frontend in development mode
echo [WARNING] Press Ctrl+C to stop all services
echo.
call npm run dev
goto :eof

:exit_script
echo Goodbye!
exit /b 0
