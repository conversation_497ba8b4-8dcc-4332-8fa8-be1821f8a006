version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mikrotik_monitoring_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: mikrotik_monitoring
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - mikrotik_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d mikrotik_monitoring"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: mikrotik_monitoring_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - mikrotik_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mikrotik_monitoring_backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ***********************************************/mikrotik_monitoring?schema=public
      REDIS_URL: redis://:redis123@redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_EXPIRES_IN: 7d
      JWT_REFRESH_EXPIRES_IN: 30d
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_SECURE: false
      SMTP_USER: <EMAIL>
      SMTP_PASS: your-app-password
      EMAIL_FROM: <EMAIL>
      MONITORING_INTERVAL: 30000
      ALERT_CHECK_INTERVAL: 60000
      CLEANUP_INTERVAL: 3600000
      BCRYPT_ROUNDS: 12
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      CORS_ORIGIN: http://localhost:3000
      CORS_CREDENTIALS: true
      LOG_LEVEL: info
      DEFAULT_ADMIN_EMAIL: <EMAIL>
      DEFAULT_ADMIN_PASSWORD: admin123
      DEFAULT_ADMIN_USERNAME: admin
      ENABLE_EMAIL_NOTIFICATIONS: true
      ENABLE_SMS_NOTIFICATIONS: false
      ENABLE_AUDIT_LOGGING: true
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    ports:
      - "3001:3001"
    networks:
      - mikrotik_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_URL: http://localhost:3001/api/v1
        NEXT_PUBLIC_WS_URL: http://localhost:3001
        NEXT_PUBLIC_APP_NAME: MikroTik Monitoring System
        NEXT_PUBLIC_APP_VERSION: 1.0.0
    container_name: mikrotik_monitoring_frontend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:3001/api/v1
      NEXT_PUBLIC_WS_URL: http://localhost:3001
      NEXT_PUBLIC_APP_NAME: MikroTik Monitoring System
      NEXT_PUBLIC_APP_VERSION: 1.0.0
    ports:
      - "3000:3000"
    networks:
      - mikrotik_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: mikrotik_monitoring_nginx
    restart: unless-stopped
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - mikrotik_network
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring and Alerting (Prometheus - Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: mikrotik_monitoring_prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - mikrotik_network
    profiles:
      - monitoring

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: mikrotik_monitoring_grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - mikrotik_network
    depends_on:
      - prometheus
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  mikrotik_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
