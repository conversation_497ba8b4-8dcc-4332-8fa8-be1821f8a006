<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user'])) {
    header("Location: ../login.php");
    exit();
}

$user = $_SESSION['user'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - الرصين</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(120deg, #f8fafc 0%, #e0e7ff 100%);
            font-family: 'Tajawal', sans-serif;
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #6366f1 0%, #0ea5e9 100%);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            text-align: center;
            padding: 1.5rem;
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .btn-logout {
            background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
            border: none;
            color: white;
            border-radius: 20px;
            padding: 8px 20px;
        }
        
        .btn-logout:hover {
            color: white;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                📡 الرصين
            </a>
            
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['username']); ?></span>
                <span class="badge bg-primary me-3"><?php echo htmlspecialchars($user['role']); ?></span>
                <a href="../logout.php" class="btn btn-logout btn-sm">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <h2 class="mb-3">مرحباً بك في نظام الرصين</h2>
            <p class="mb-0">منصة تنظيم التواصل بين وحدات قسم الإتصالات</p>
            <small class="opacity-75">تم تسجيل الدخول بنجاح كـ <?php echo htmlspecialchars($user['role']); ?></small>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body stat-card">
                        <div class="stat-icon">📊</div>
                        <h5>الإحصائيات</h5>
                        <p class="text-muted">عرض التقارير</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body stat-card">
                        <div class="stat-icon">👥</div>
                        <h5>المستخدمين</h5>
                        <p class="text-muted">إدارة الحسابات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body stat-card">
                        <div class="stat-icon">📋</div>
                        <h5>المهام</h5>
                        <p class="text-muted">متابعة الأعمال</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card dashboard-card">
                    <div class="card-body stat-card">
                        <div class="stat-icon">⚙️</div>
                        <h5>الإعدادات</h5>
                        <p class="text-muted">تخصيص النظام</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5 class="mb-0">الإجراءات السريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary">إضافة مهمة جديدة</button>
                            <button class="btn btn-outline-success">عرض التقارير</button>
                            <button class="btn btn-outline-info">إدارة المستخدمين</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <h5 class="mb-0">معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><strong>الإصدار:</strong> 2.0</li>
                            <li><strong>آخر تحديث:</strong> <?php echo date('Y-m-d'); ?></li>
                            <li><strong>حالة النظام:</strong> <span class="text-success">يعمل بشكل طبيعي</span></li>
                            <li><strong>المستخدم:</strong> <?php echo htmlspecialchars($user['username']); ?></li>
                            <li><strong>الدور:</strong> <?php echo htmlspecialchars($user['role']); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
