import { prisma } from '@/services/database';
import { notificationService } from '@/services/notification';
import { socketService } from '@/services/socket';
import { logger } from '@/utils/logger';
import { AlertType, AlertSeverity, AlertStatus } from '@prisma/client';

export interface CreateAlertData {
  deviceId: string;
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  threshold?: number | null;
  currentValue?: number | null;
  assignedToId?: string;
}

export interface UpdateAlertData {
  status?: AlertStatus;
  assignedToId?: string;
  resolvedAt?: Date;
}

class AlertService {
  private static instance: AlertService;
  private alertCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): AlertService {
    if (!AlertService.instance) {
      AlertService.instance = new AlertService();
    }
    return AlertService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = AlertService.getInstance();
    await instance.start();
  }

  public static async stop(): Promise<void> {
    const instance = AlertService.getInstance();
    await instance.stop();
  }

  public async start(): Promise<void> {
    try {
      // Start alert checking interval
      this.alertCheckInterval = setInterval(async () => {
        await this.processAlerts();
      }, 60000); // Check every minute

      logger.info('Alert service started');
    } catch (error) {
      logger.error('Failed to start alert service:', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      if (this.alertCheckInterval) {
        clearInterval(this.alertCheckInterval);
        this.alertCheckInterval = null;
      }

      logger.info('Alert service stopped');
    } catch (error) {
      logger.error('Error stopping alert service:', error);
      throw error;
    }
  }

  /**
   * Create a new alert
   */
  public async createAlert(data: CreateAlertData): Promise<any> {
    try {
      // Check if similar alert already exists and is active
      const existingAlert = await prisma.alert.findFirst({
        where: {
          deviceId: data.deviceId,
          type: data.type,
          status: 'ACTIVE',
          createdAt: {
            gte: new Date(Date.now() - 5 * 60 * 1000), // Within last 5 minutes
          },
        },
      });

      if (existingAlert) {
        // Update existing alert with new values
        const updatedAlert = await prisma.alert.update({
          where: { id: existingAlert.id },
          data: {
            message: data.message,
            currentValue: data.currentValue,
            updatedAt: new Date(),
          },
          include: {
            device: true,
            assignedTo: true,
          },
        });

        logger.debug(`Updated existing alert: ${updatedAlert.id}`);
        return updatedAlert;
      }

      // Create new alert
      const alert = await prisma.alert.create({
        data: {
          deviceId: data.deviceId,
          type: data.type,
          severity: data.severity,
          title: data.title,
          message: data.message,
          threshold: data.threshold,
          currentValue: data.currentValue,
          assignedToId: data.assignedToId,
          status: 'ACTIVE',
        },
        include: {
          device: true,
          assignedTo: true,
        },
      });

      logger.info(`Created new alert: ${alert.id} - ${alert.title}`);

      // Send notifications
      await this.sendAlertNotifications(alert);

      // Emit real-time event
      socketService.emitNewAlert(alert);

      return alert;

    } catch (error) {
      logger.error('Error creating alert:', error);
      throw error;
    }
  }

  /**
   * Update an alert
   */
  public async updateAlert(alertId: string, data: UpdateAlertData): Promise<any> {
    try {
      const alert = await prisma.alert.update({
        where: { id: alertId },
        data: {
          ...data,
          updatedAt: new Date(),
        },
        include: {
          device: true,
          assignedTo: true,
        },
      });

      logger.info(`Updated alert: ${alert.id} - Status: ${alert.status}`);

      // Emit real-time event
      socketService.emitAlertUpdate(alert);

      return alert;

    } catch (error) {
      logger.error('Error updating alert:', error);
      throw error;
    }
  }

  /**
   * Acknowledge an alert
   */
  public async acknowledgeAlert(alertId: string, userId: string): Promise<any> {
    try {
      const alert = await this.updateAlert(alertId, {
        status: 'ACKNOWLEDGED',
        assignedToId: userId,
      });

      logger.info(`Alert acknowledged: ${alert.id} by user: ${userId}`);
      return alert;

    } catch (error) {
      logger.error('Error acknowledging alert:', error);
      throw error;
    }
  }

  /**
   * Resolve an alert
   */
  public async resolveAlert(alertId: string, userId: string): Promise<any> {
    try {
      const alert = await this.updateAlert(alertId, {
        status: 'RESOLVED',
        resolvedAt: new Date(),
        assignedToId: userId,
      });

      logger.info(`Alert resolved: ${alert.id} by user: ${userId}`);
      return alert;

    } catch (error) {
      logger.error('Error resolving alert:', error);
      throw error;
    }
  }

  /**
   * Get alerts with filtering and pagination
   */
  public async getAlerts(options: {
    page?: number;
    limit?: number;
    status?: AlertStatus;
    severity?: AlertSeverity;
    type?: AlertType;
    deviceId?: string;
    assignedToId?: string;
  } = {}): Promise<{
    alerts: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        severity,
        type,
        deviceId,
        assignedToId,
      } = options;

      const skip = (page - 1) * limit;

      const where: any = {};
      if (status) where.status = status;
      if (severity) where.severity = severity;
      if (type) where.type = type;
      if (deviceId) where.deviceId = deviceId;
      if (assignedToId) where.assignedToId = assignedToId;

      const [alerts, total] = await Promise.all([
        prisma.alert.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            device: true,
            assignedTo: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        }),
        prisma.alert.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        alerts,
        total,
        page,
        limit,
        totalPages,
      };

    } catch (error) {
      logger.error('Error getting alerts:', error);
      throw error;
    }
  }

  /**
   * Get alert statistics
   */
  public async getAlertStatistics(): Promise<{
    total: number;
    active: number;
    acknowledged: number;
    resolved: number;
    bySeverity: { [key: string]: number };
    byType: { [key: string]: number };
  }> {
    try {
      const [
        total,
        active,
        acknowledged,
        resolved,
        bySeverity,
        byType,
      ] = await Promise.all([
        prisma.alert.count(),
        prisma.alert.count({ where: { status: 'ACTIVE' } }),
        prisma.alert.count({ where: { status: 'ACKNOWLEDGED' } }),
        prisma.alert.count({ where: { status: 'RESOLVED' } }),
        prisma.alert.groupBy({
          by: ['severity'],
          _count: { severity: true },
        }),
        prisma.alert.groupBy({
          by: ['type'],
          _count: { type: true },
        }),
      ]);

      const severityStats: { [key: string]: number } = {};
      bySeverity.forEach(item => {
        severityStats[item.severity] = item._count.severity;
      });

      const typeStats: { [key: string]: number } = {};
      byType.forEach(item => {
        typeStats[item.type] = item._count.type;
      });

      return {
        total,
        active,
        acknowledged,
        resolved,
        bySeverity: severityStats,
        byType: typeStats,
      };

    } catch (error) {
      logger.error('Error getting alert statistics:', error);
      throw error;
    }
  }

  /**
   * Process alerts (auto-resolve, escalate, etc.)
   */
  private async processAlerts(): Promise<void> {
    try {
      // Auto-resolve device down alerts if device is back online
      await this.autoResolveDeviceDownAlerts();

      // Escalate unacknowledged critical alerts
      await this.escalateCriticalAlerts();

    } catch (error) {
      logger.error('Error processing alerts:', error);
    }
  }

  private async autoResolveDeviceDownAlerts(): Promise<void> {
    try {
      // Get active device down alerts
      const deviceDownAlerts = await prisma.alert.findMany({
        where: {
          type: 'DEVICE_DOWN',
          status: 'ACTIVE',
        },
        include: { device: true },
      });

      for (const alert of deviceDownAlerts) {
        // Check if device is back online (has recent lastSeen)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        if (alert.device.lastSeen && alert.device.lastSeen > fiveMinutesAgo) {
          await this.updateAlert(alert.id, {
            status: 'RESOLVED',
            resolvedAt: new Date(),
          });

          logger.info(`Auto-resolved device down alert for ${alert.device.name}`);
        }
      }

    } catch (error) {
      logger.error('Error auto-resolving device down alerts:', error);
    }
  }

  private async escalateCriticalAlerts(): Promise<void> {
    try {
      // Get unacknowledged critical alerts older than 15 minutes
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      
      const criticalAlerts = await prisma.alert.findMany({
        where: {
          severity: 'CRITICAL',
          status: 'ACTIVE',
          createdAt: {
            lt: fifteenMinutesAgo,
          },
        },
        include: {
          device: true,
        },
      });

      for (const alert of criticalAlerts) {
        // Send escalation notifications
        await this.sendEscalationNotifications(alert);
        logger.info(`Escalated critical alert: ${alert.id}`);
      }

    } catch (error) {
      logger.error('Error escalating critical alerts:', error);
    }
  }

  private async sendAlertNotifications(alert: any): Promise<void> {
    try {
      // Get users who should receive notifications
      const users = await prisma.user.findMany({
        where: {
          isActive: true,
          role: {
            in: ['ADMIN', 'MANAGER'],
          },
        },
      });

      // Send notifications to each user
      for (const user of users) {
        await notificationService.sendAlertNotification(user, alert);
      }

    } catch (error) {
      logger.error('Error sending alert notifications:', error);
    }
  }

  private async sendEscalationNotifications(alert: any): Promise<void> {
    try {
      // Get admin users for escalation
      const adminUsers = await prisma.user.findMany({
        where: {
          isActive: true,
          role: 'ADMIN',
        },
      });

      // Send escalation notifications
      for (const user of adminUsers) {
        await notificationService.sendEscalationNotification(user, alert);
      }

    } catch (error) {
      logger.error('Error sending escalation notifications:', error);
    }
  }
}

export const alertService = AlertService.getInstance();
export { AlertService };
