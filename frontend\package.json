{"name": "mikrotik-monitoring-frontend", "version": "1.0.0", "description": "Frontend for MikroTik Network Monitoring System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.2", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "date-fns": "^2.30.0", "react-datepicker": "^4.25.0", "@types/react-datepicker": "^4.19.4", "react-select": "^5.8.0", "react-modal": "^3.16.1", "@types/react-modal": "^3.16.3", "react-tooltip": "^5.25.0", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.3", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "classnames": "^2.3.2"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}