import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { prisma } from '@/services/database';
import { logger } from '@/utils/logger';
import { config } from '@/config/config';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
}

class SocketService {
  private static instance: SocketService;
  private io: Server | null = null;
  private connectedUsers: Map<string, string[]> = new Map(); // userId -> socketIds[]

  private constructor() {}

  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  public static initialize(io: Server): void {
    const instance = SocketService.getInstance();
    instance.setupSocketServer(io);
  }

  private setupSocketServer(io: Server): void {
    this.io = io;

    // Authentication middleware
    io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth['token'] || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwt.secret) as any;
        const user = await prisma.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            username: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
          },
        });

        if (!user || !user.isActive) {
          return next(new Error('Invalid or inactive user'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();

      } catch (error) {
        logger.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Connection handling
    io.on('connection', (socket: AuthenticatedSocket) => {
      this.handleConnection(socket);
    });

    logger.info('Socket.IO server initialized');
  }

  private handleConnection(socket: AuthenticatedSocket): void {
    const userId = socket.userId!;
    const socketId = socket.id;

    logger.info(`User ${userId} connected with socket ${socketId}`);

    // Track connected user
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, []);
    }
    this.connectedUsers.get(userId)!.push(socketId);

    // Join user-specific room
    socket.join(`user:${userId}`);

    // Join role-specific rooms
    if (socket.user.role === 'ADMIN') {
      socket.join('admins');
    }
    if (['ADMIN', 'MANAGER'].includes(socket.user.role)) {
      socket.join('managers');
    }

    // Handle device subscription
    socket.on('subscribe:device', (deviceId: string) => {
      this.handleDeviceSubscription(socket, deviceId);
    });

    socket.on('unsubscribe:device', (deviceId: string) => {
      this.handleDeviceUnsubscription(socket, deviceId);
    });

    // Handle alert actions
    socket.on('alert:acknowledge', (data: { alertId: string }) => {
      this.handleAlertAcknowledge(socket, data.alertId);
    });

    socket.on('alert:resolve', (data: { alertId: string }) => {
      this.handleAlertResolve(socket, data.alertId);
    });

    // Handle dashboard subscription
    socket.on('subscribe:dashboard', () => {
      socket.join('dashboard');
      logger.debug(`User ${userId} subscribed to dashboard updates`);
    });

    socket.on('unsubscribe:dashboard', () => {
      socket.leave('dashboard');
      logger.debug(`User ${userId} unsubscribed from dashboard updates`);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });

    // Send initial connection confirmation
    socket.emit('connected', {
      message: 'Connected to MikroTik Monitoring System',
      user: socket.user,
      timestamp: new Date().toISOString(),
    });
  }

  private async handleDeviceSubscription(socket: AuthenticatedSocket, deviceId: string): Promise<void> {
    try {
      // Check if user has access to this device
      const device = await prisma.device.findFirst({
        where: {
          id: deviceId,
          OR: [
            { ownerId: socket.userId },
            { owner: { role: { in: ['ADMIN', 'MANAGER'] } } },
          ],
        },
      });

      if (!device) {
        socket.emit('error', { message: 'Device not found or access denied' });
        return;
      }

      socket.join(`device:${deviceId}`);
      logger.debug(`User ${socket.userId} subscribed to device ${deviceId}`);

      socket.emit('device:subscribed', { deviceId });

    } catch (error) {
      logger.error('Error handling device subscription:', error);
      socket.emit('error', { message: 'Failed to subscribe to device' });
    }
  }

  private handleDeviceUnsubscription(socket: AuthenticatedSocket, deviceId: string): void {
    socket.leave(`device:${deviceId}`);
    logger.debug(`User ${socket.userId} unsubscribed from device ${deviceId}`);
    socket.emit('device:unsubscribed', { deviceId });
  }

  private async handleAlertAcknowledge(socket: AuthenticatedSocket, alertId: string): Promise<void> {
    try {
      // Import alertService here to avoid circular dependency
      const { alertService } = await import('@/services/alert');
      await alertService.acknowledgeAlert(alertId, socket.userId!);
      
      socket.emit('alert:acknowledged', { alertId });
      logger.info(`Alert ${alertId} acknowledged by user ${socket.userId}`);

    } catch (error) {
      logger.error('Error acknowledging alert:', error);
      socket.emit('error', { message: 'Failed to acknowledge alert' });
    }
  }

  private async handleAlertResolve(socket: AuthenticatedSocket, alertId: string): Promise<void> {
    try {
      // Import alertService here to avoid circular dependency
      const { alertService } = await import('@/services/alert');
      await alertService.resolveAlert(alertId, socket.userId!);
      
      socket.emit('alert:resolved', { alertId });
      logger.info(`Alert ${alertId} resolved by user ${socket.userId}`);

    } catch (error) {
      logger.error('Error resolving alert:', error);
      socket.emit('error', { message: 'Failed to resolve alert' });
    }
  }

  private handleDisconnection(socket: AuthenticatedSocket): void {
    const userId = socket.userId!;
    const socketId = socket.id;

    logger.info(`User ${userId} disconnected (socket ${socketId})`);

    // Remove socket from user's connected sockets
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      const index = userSockets.indexOf(socketId);
      if (index > -1) {
        userSockets.splice(index, 1);
      }

      // Remove user from map if no more sockets
      if (userSockets.length === 0) {
        this.connectedUsers.delete(userId);
      }
    }
  }

  // Public methods for emitting events

  /**
   * Emit device metrics to subscribers
   */
  public emitDeviceMetrics(deviceId: string, metrics: any): void {
    if (!this.io) return;

    this.io.to(`device:${deviceId}`).emit('device:metrics', {
      deviceId,
      metrics,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Emit device down event
   */
  public emitDeviceDown(deviceId: string): void {
    if (!this.io) return;

    this.io.to(`device:${deviceId}`).emit('device:down', {
      deviceId,
      timestamp: new Date().toISOString(),
    });

    // Also emit to dashboard subscribers
    this.io.to('dashboard').emit('device:status:changed', {
      deviceId,
      status: 'down',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Emit new alert to relevant users
   */
  public emitNewAlert(alert: any): void {
    if (!this.io) return;

    // Emit to device subscribers
    this.io.to(`device:${alert.deviceId}`).emit('alert:new', alert);

    // Emit to managers and admins
    this.io.to('managers').emit('alert:new', alert);

    // Emit to dashboard subscribers
    this.io.to('dashboard').emit('dashboard:alert:new', alert);
  }

  /**
   * Emit alert update
   */
  public emitAlertUpdate(alert: any): void {
    if (!this.io) return;

    // Emit to device subscribers
    this.io.to(`device:${alert.deviceId}`).emit('alert:updated', alert);

    // Emit to managers and admins
    this.io.to('managers').emit('alert:updated', alert);

    // Emit to dashboard subscribers
    this.io.to('dashboard').emit('dashboard:alert:updated', alert);
  }

  /**
   * Emit system notification
   */
  public emitSystemNotification(message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    if (!this.io) return;

    this.io.emit('system:notification', {
      message,
      type,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Emit dashboard statistics update
   */
  public emitDashboardUpdate(data: any): void {
    if (!this.io) return;

    this.io.to('dashboard').emit('dashboard:update', {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get connected users count
   */
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Get connected sockets count
   */
  public getConnectedSocketsCount(): number {
    let count = 0;
    this.connectedUsers.forEach(sockets => {
      count += sockets.length;
    });
    return count;
  }

  /**
   * Check if user is connected
   */
  public isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  /**
   * Send message to specific user
   */
  public sendToUser(userId: string, event: string, data: any): void {
    if (!this.io) return;

    this.io.to(`user:${userId}`).emit(event, data);
  }
}

export const socketService = SocketService.getInstance();
export { SocketService };
