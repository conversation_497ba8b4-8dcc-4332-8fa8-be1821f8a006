<?php
// ملف الدوال المساعدة

// منع الوصول المباشر
if (!defined('SECURE_ACCESS')) {
    die('الوصول المباشر غير مسموح');
}

// دالة تحويل التاريخ إلى العربية
function formatArabicDate($date, $format = 'Y-m-d H:i:s') {
    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $arabic_days = [
        'Sunday' => 'الأحد', 'Monday' => 'الإثنين', 'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء', 'Thursday' => 'الخميس', 'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ];
    
    $timestamp = strtotime($date);
    $day = date('j', $timestamp);
    $month = $arabic_months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    $day_name = $arabic_days[date('l', $timestamp)];
    $time = date('H:i', $timestamp);
    
    return "{$day_name} {$day} {$month} {$year} - {$time}";
}

// دالة تحويل الأرقام إلى العربية
function convertToArabicNumbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english_numbers, $arabic_numbers, $string);
}

// دالة تحويل الأرقام إلى الإنجليزية
function convertToEnglishNumbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($arabic_numbers, $english_numbers, $string);
}

// دالة إنشاء معرف فريد
function generateUniqueId($prefix = '') {
    return $prefix . uniqid() . '_' . mt_rand(1000, 9999);
}

// دالة تقصير النص
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length, 'UTF-8') . $suffix;
}

// دالة تنسيق حجم الملف
function formatFileSize($bytes, $precision = 2) {
    $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// دالة إرسال الإشعارات
function sendNotification($user_id, $title, $message, $type = 'info') {
    global $conn;
    
    $stmt = $conn->prepare("INSERT INTO notifications (user_id, title, message, type, created_at) VALUES (?, ?, ?, ?, NOW())");
    $stmt->bind_param("isss", $user_id, $title, $message, $type);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

// دالة الحصول على الإشعارات غير المقروءة
function getUnreadNotifications($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $notifications = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    return $notifications;
}

// دالة تحديد حالة الإشعار كمقروء
function markNotificationAsRead($notification_id, $user_id) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $notification_id, $user_id);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

// دالة إنشاء كلمة مرور عشوائية
function generateRandomPassword($length = 12) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[random_int(0, strlen($characters) - 1)];
    }
    
    return $password;
}

// دالة التحقق من صحة رقم الهاتف العراقي
function validateIraqiPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // أنماط أرقام الهواتف العراقية
    $patterns = [
        '/^(\+964|964|0)?7[0-9]{9}$/',  // أرقام الجوال
        '/^(\+964|964|0)?1[0-9]{8}$/',  // أرقام الخط الثابت في بغداد
        '/^(\+964|964|0)?[2-6][0-9]{7,8}$/' // أرقام الخط الثابت في المحافظات
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $phone)) {
            return true;
        }
    }
    
    return false;
}

// دالة تنسيق رقم الهاتف العراقي
function formatIraqiPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) == 11 && substr($phone, 0, 1) == '0') {
        $phone = substr($phone, 1);
    }
    
    if (strlen($phone) == 10 && substr($phone, 0, 1) == '7') {
        return '+964 ' . substr($phone, 0, 3) . ' ' . substr($phone, 3, 3) . ' ' . substr($phone, 6);
    }
    
    return $phone;
}

// دالة إنشاء رمز التحقق
function generateVerificationCode($length = 6) {
    return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

// دالة التحقق من انتهاء صلاحية الرمز
function isCodeExpired($created_at, $expiry_minutes = 15) {
    $created_timestamp = strtotime($created_at);
    $current_timestamp = time();
    $expiry_seconds = $expiry_minutes * 60;
    
    return ($current_timestamp - $created_timestamp) > $expiry_seconds;
}

// دالة تنظيف البيانات القديمة
function cleanupOldData() {
    global $conn;
    
    // حذف محاولات تسجيل الدخول القديمة (أكثر من 30 يوم)
    $conn->query("DELETE FROM login_attempts WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    
    // حذف سجلات تسجيل الدخول القديمة (أكثر من 90 يوم)
    $conn->query("DELETE FROM login_logs WHERE login_time < DATE_SUB(NOW(), INTERVAL 90 DAY)");
    
    // حذف الإشعارات المقروءة القديمة (أكثر من 30 يوم)
    $conn->query("DELETE FROM notifications WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    
    // حذف سجلات الأنشطة القديمة (أكثر من 180 يوم)
    $conn->query("DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 180 DAY)");
}

// دالة إنشاء نسخة احتياطية من قاعدة البيانات
function createDatabaseBackup() {
    global $conn;
    
    $backup_dir = __DIR__ . '/../backups/';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    // هذه دالة مبسطة - في الواقع تحتاج لاستخدام mysqldump
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }
    
    $backup_content = "-- نسخة احتياطية من قاعدة البيانات\n";
    $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
    
    foreach ($tables as $table) {
        $backup_content .= "-- بيانات الجدول: {$table}\n";
        // إضافة بيانات الجدول هنا
    }
    
    return file_put_contents($backup_file, $backup_content);
}
?>
