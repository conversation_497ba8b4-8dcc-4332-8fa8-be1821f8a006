import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface Config {
  // Server Configuration
  port: number;
  nodeEnv: string;
  apiVersion: string;

  // Database Configuration
  database: {
    url: string;
  };

  // JWT Configuration
  jwt: {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };

  // Redis Configuration
  redis: {
    url: string;
    password?: string;
    db: number;
  };

  // Email Configuration
  email: {
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      user: string;
      pass: string;
    };
    from: string;
  };

  // SMS Configuration
  sms: {
    twilio: {
      accountSid: string;
      authToken: string;
      phoneNumber: string;
    };
  };

  // Monitoring Configuration
  monitoring: {
    interval: number;
    alertCheckInterval: number;
    cleanupInterval: number;
  };

  // Security Configuration
  security: {
    bcryptRounds: number;
  };

  // Rate Limiting Configuration
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };

  // CORS Configuration
  cors: {
    origin: string | string[];
    credentials: boolean;
  };

  // Logging Configuration
  logging: {
    level: string;
    file: string;
    maxSize: string;
    maxFiles: number;
  };

  // Default Admin Configuration
  defaultAdmin: {
    email: string;
    password: string;
    username: string;
  };

  // MikroTik Configuration
  mikrotik: {
    defaultApiPort: number;
    defaultSnmpPort: number;
    defaultSnmpCommunity: string;
  };

  // Feature Flags
  features: {
    enableSnmp: boolean;
    enableEmailNotifications: boolean;
    enableSmsNotifications: boolean;
    enableWebhookNotifications: boolean;
    enableAuditLogging: boolean;
    enableMetricsCleanup: boolean;
  };
}

const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  return value;
};

const getEnvNumber = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid number`);
  }
  return parsed;
};

const getEnvBoolean = (key: string, defaultValue?: boolean): boolean => {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  return value.toLowerCase() === 'true';
};

export const config: Config = {
  // Server Configuration
  port: getEnvNumber('PORT', 3001),
  nodeEnv: getEnvVar('NODE_ENV', 'development'),
  apiVersion: getEnvVar('API_VERSION', 'v1'),

  // Database Configuration
  database: {
    url: getEnvVar('DATABASE_URL'),
  },

  // JWT Configuration
  jwt: {
    secret: getEnvVar('JWT_SECRET'),
    expiresIn: getEnvVar('JWT_EXPIRES_IN', '7d'),
    refreshExpiresIn: getEnvVar('JWT_REFRESH_EXPIRES_IN', '30d'),
  },

  // Redis Configuration
  redis: {
    url: getEnvVar('REDIS_URL', 'redis://localhost:6379'),
    password: process.env['REDIS_PASSWORD'],
    db: getEnvNumber('REDIS_DB', 0),
  },

  // Email Configuration
  email: {
    smtp: {
      host: getEnvVar('SMTP_HOST', 'smtp.gmail.com'),
      port: getEnvNumber('SMTP_PORT', 587),
      secure: getEnvBoolean('SMTP_SECURE', false),
      user: getEnvVar('SMTP_USER'),
      pass: getEnvVar('SMTP_PASS'),
    },
    from: getEnvVar('EMAIL_FROM', '<EMAIL>'),
  },

  // SMS Configuration
  sms: {
    twilio: {
      accountSid: getEnvVar('TWILIO_ACCOUNT_SID', ''),
      authToken: getEnvVar('TWILIO_AUTH_TOKEN', ''),
      phoneNumber: getEnvVar('TWILIO_PHONE_NUMBER', ''),
    },
  },

  // Monitoring Configuration
  monitoring: {
    interval: getEnvNumber('MONITORING_INTERVAL', 30000), // 30 seconds
    alertCheckInterval: getEnvNumber('ALERT_CHECK_INTERVAL', 60000), // 1 minute
    cleanupInterval: getEnvNumber('CLEANUP_INTERVAL', 3600000), // 1 hour
  },

  // Security Configuration
  security: {
    bcryptRounds: getEnvNumber('BCRYPT_ROUNDS', 12),
  },

  // Rate Limiting Configuration
  rateLimit: {
    windowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 900000), // 15 minutes
    maxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
  },

  // CORS Configuration
  cors: {
    origin: getEnvVar('CORS_ORIGIN', 'http://localhost:3000'),
    credentials: getEnvBoolean('CORS_CREDENTIALS', true),
  },

  // Logging Configuration
  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
    file: getEnvVar('LOG_FILE', 'logs/app.log'),
    maxSize: getEnvVar('LOG_MAX_SIZE', '10m'),
    maxFiles: getEnvNumber('LOG_MAX_FILES', 5),
  },

  // Default Admin Configuration
  defaultAdmin: {
    email: getEnvVar('DEFAULT_ADMIN_EMAIL', '<EMAIL>'),
    password: getEnvVar('DEFAULT_ADMIN_PASSWORD', 'admin123'),
    username: getEnvVar('DEFAULT_ADMIN_USERNAME', 'admin'),
  },

  // MikroTik Configuration
  mikrotik: {
    defaultApiPort: getEnvNumber('DEFAULT_API_PORT', 8728),
    defaultSnmpPort: getEnvNumber('DEFAULT_SNMP_PORT', 161),
    defaultSnmpCommunity: getEnvVar('DEFAULT_SNMP_COMMUNITY', 'public'),
  },

  // Feature Flags
  features: {
    enableSnmp: getEnvBoolean('ENABLE_SNMP', true),
    enableEmailNotifications: getEnvBoolean('ENABLE_EMAIL_NOTIFICATIONS', true),
    enableSmsNotifications: getEnvBoolean('ENABLE_SMS_NOTIFICATIONS', false),
    enableWebhookNotifications: getEnvBoolean('ENABLE_WEBHOOK_NOTIFICATIONS', true),
    enableAuditLogging: getEnvBoolean('ENABLE_AUDIT_LOGGING', true),
    enableMetricsCleanup: getEnvBoolean('ENABLE_METRICS_CLEANUP', true),
  },
};

// Validate critical configuration
const validateConfig = () => {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
  ];

  const missing = requiredVars.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate JWT secret strength
  if (config.jwt.secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  // Validate email configuration if email notifications are enabled
  if (config.features.enableEmailNotifications) {
    if (!config.email.smtp.user || !config.email.smtp.pass) {
      throw new Error('Email configuration is incomplete. SMTP_USER and SMTP_PASS are required when email notifications are enabled.');
    }
  }

  // Validate SMS configuration if SMS notifications are enabled
  if (config.features.enableSmsNotifications) {
    if (!config.sms.twilio.accountSid || !config.sms.twilio.authToken) {
      throw new Error('SMS configuration is incomplete. Twilio credentials are required when SMS notifications are enabled.');
    }
  }
};

// Run validation
validateConfig();

export default config;
