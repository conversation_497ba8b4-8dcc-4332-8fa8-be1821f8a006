<?php
session_start();
require_once 'config/db.php';

$error = '';
$max_attempts = 5;
$lockout_time = 900; // 15 minutes

// التحقق من وجود جلسة مستخدم نشطة
if (isset($_SESSION['user'])) {
    // توجيه المستخدم إلى الصفحة المناسبة بناءً على دوره
    $role = $_SESSION['user']['role'];
    switch ($role) {
        case 'الإدارة العامة':
            header("Location: dashboard/index.php");
            exit();
        case 'مدير':
            header("Location: admin/index.php");
            exit();
        case 'إدارة الجوال':
            header("Location: mobile/index.php");
            exit();
        case 'المخابر':
            header("Location: lab/index.php");
            exit();
        case 'الهندسة':
            header("Location: engineering/index.php");
            exit();
        case 'الصيانة':
            header("Location: maintenance/index.php");
            exit();
        default:
            header("Location: index.php");
            exit();
    }
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($password)) {
        $error = "يرجى إدخال اسم المستخدم وكلمة المرور";
    } else {
        // التحقق من محاولات تسجيل الدخول الفاشلة
        $ip = $_SERVER['REMOTE_ADDR'];
        $check_attempts = $conn->prepare("SELECT attempts, last_attempt FROM login_attempts WHERE ip = ? AND last_attempt > DATE_SUB(NOW(), INTERVAL ? SECOND)");
        $check_attempts->bind_param("si", $ip, $lockout_time);
        $check_attempts->execute();
        $attempt_result = $check_attempts->get_result();
        
        if ($attempt_result->num_rows > 0) {
            $attempt_data = $attempt_result->fetch_assoc();
            if ($attempt_data['attempts'] >= $max_attempts) {
                $error = "تم حظر عنوان IP الخاص بك مؤقتاً بسبب المحاولات المتكررة. حاول مرة أخرى بعد 15 دقيقة.";
            }
        }
        
        if (empty($error)) {
            // استخدام Prepared Statement لمنع SQL Injection
            $stmt = $conn->prepare("SELECT id, username, ar_name, role, password FROM users WHERE username = ? AND active = 1");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();

                // التحقق من كلمة المرور
                if (password_verify($password, $user['password'])) {
                    // مسح محاولات تسجيل الدخول الفاشلة
                    $clear_attempts = $conn->prepare("DELETE FROM login_attempts WHERE ip = ?");
                    $clear_attempts->bind_param("s", $ip);
                    $clear_attempts->execute();
                    
                    // تسجيل عملية تسجيل الدخول الناجحة
                    $log_login = $conn->prepare("INSERT INTO login_logs (user_id, ip, user_agent, login_time) VALUES (?, ?, ?, NOW())");
                    $user_agent = $_SERVER['HTTP_USER_AGENT'];
                    $log_login->bind_param("iss", $user['id'], $ip, $user_agent);
                    $log_login->execute();
                    
                    // تخزين معلومات المستخدم في الجلسة
                    $_SESSION['user'] = [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'ar_name' => $user['ar_name'],
                        'role' => $user['role'],
                        'login_time' => time()
                    ];

                    // توجيه المستخدم إلى الصفحة المناسبة بناءً على دوره
                    switch ($user['role']) {
                        case 'الإدارة العامة':
                            header("Location: dashboard/index.php");
                            exit();
                        case 'مدير':
                            header("Location: admin/index.php");
                            exit();
                        case 'إدارة الجوال':
                            header("Location: mobile/index.php");
                            exit();
                        case 'المخابر':
                            header("Location: lab/index.php");
                            exit();
                        case 'الهندسة':
                            header("Location: engineering/index.php");
                            exit();
                        case 'الصيانة':
                            header("Location: maintenance/index.php");
                            exit();
                        default:
                            header("Location: index.php");
                            exit();
                    }
                } else {
                    $error = "كلمة المرور غير صحيحة";
                    // تسجيل محاولة تسجيل دخول فاشلة
                    $record_attempt = $conn->prepare("INSERT INTO login_attempts (ip, attempts, last_attempt) VALUES (?, 1, NOW()) ON DUPLICATE KEY UPDATE attempts = attempts + 1, last_attempt = NOW()");
                    $record_attempt->bind_param("s", $ip);
                    $record_attempt->execute();
                }
            } else {
                $error = "اسم المستخدم غير موجود أو غير مفعل";
                // تسجيل محاولة تسجيل دخول فاشلة
                $record_attempt = $conn->prepare("INSERT INTO login_attempts (ip, attempts, last_attempt) VALUES (?, 1, NOW()) ON DUPLICATE KEY UPDATE attempts = attempts + 1, last_attempt = NOW()");
                $record_attempt->bind_param("s", $ip);
                $record_attempt->execute();
            }
            $stmt->close();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - الرصين</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #0dcaf0 0%, #6610f2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Tajawal', sans-serif;
        }
        .login-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            padding: 2.5rem 2rem 2rem 2rem;
            max-width: 400px;
            width: 100%;
        }
        .login-card .form-label {
            font-weight: 500;
        }
        .login-card .btn-custom {
            background: linear-gradient(90deg, #0dcaf0 0%, #6610f2 100%);
            color: #fff;
            font-weight: bold;
            border: none;
            transition: 0.2s;
        }
        .login-card .btn-custom:hover {
            background: linear-gradient(90deg, #6610f2 0%, #0dcaf0 100%);
            color: #fff;
            transform: translateY(-2px);
        }
        .login-logo {
            font-size: 2.8rem;
            color: #6610f2;
            margin-bottom: 1rem;
            text-align: center;
        }
        .form-control:focus {
            border-color: #6610f2;
            box-shadow: 0 0 0 0.2rem rgba(102, 16, 242, 0.25);
        }
    </style>
</head>
<body>
    <div class="login-card mx-auto">
        <div class="login-logo">
            <span>🔐</span>
        </div>
        <h4 class="mb-4 text-center fw-bold">تسجيل الدخول</h4>
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger text-center"><?php echo htmlspecialchars($error); ?></div> 
        <?php endif; ?>
        <form method="POST" autocomplete="off">
            <div class="mb-3">
                <label for="username" class="form-label">اسم المستخدم</label>       
                <input type="text" class="form-control" id="username" name="username" required placeholder="أدخل اسم المستخدم" maxlength="50">
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">كلمة المرور</label>        
                <input type="password" class="form-control" id="password" name="password" required placeholder="أدخل كلمة المرور" maxlength="100">
            </div>
            <button type="submit" class="btn btn-custom w-100 mt-3">دخول</button>   
        </form>
        <div class="text-center mt-3">
            <small class="text-muted">© <?php echo date('Y'); ?> الرصين - جميع الحقوق محفوظة</small>
        </div>
    </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
