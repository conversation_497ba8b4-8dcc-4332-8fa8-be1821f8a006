# دليل التثبيت والتشغيل
# MikroTik Monitoring System Installation Guide

## المتطلبات الأساسية
### System Requirements

- **Operating System**: Linux, macOS, or Windows
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: Minimum 10GB free space
- **Network**: Internet connection for initial setup

### MikroTik Device Requirements

- **RouterOS**: Version 6.40 or higher
- **API Access**: Enabled on port 8728 (default)
- **User Account**: With API permissions
- **Network**: Accessible from monitoring server

## التثبيت السريع باستخدام Docker
## Quick Installation with Docker

### 1. استنساخ المشروع
### Clone the Repository

```bash
git clone https://github.com/your-repo/mikrotik-monitoring.git
cd mikrotik-monitoring
```

### 2. إعداد متغيرات البيئة
### Configure Environment Variables

```bash
# نسخ ملف الإعدادات
cp backend/.env.example backend/.env

# تحرير الإعدادات
nano backend/.env
```

#### إعدادات قاعدة البيانات
#### Database Configuration

```env
DATABASE_URL="***********************************************/mikrotik_monitoring?schema=public"
```

#### إعدادات JWT
#### JWT Configuration

```env
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_EXPIRES_IN="30d"
```

#### إعدادات البريد الإلكتروني
#### Email Configuration

```env
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"
```

### 3. تشغيل النظام
### Start the System

```bash
# تشغيل جميع الخدمات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

### 4. التحقق من التشغيل
### Verify Installation

```bash
# فحص حالة الخدمات
docker-compose ps

# فحص صحة النظام
curl http://localhost:3001/health
curl http://localhost:3000
```

## التثبيت اليدوي
## Manual Installation

### 1. تثبيت قاعدة البيانات
### Database Setup

#### PostgreSQL

```bash
# تثبيت PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# إنشاء قاعدة البيانات
sudo -u postgres createdb mikrotik_monitoring
sudo -u postgres createuser --interactive
```

#### Redis

```bash
# تثبيت Redis
sudo apt install redis-server

# تشغيل Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 2. تثبيت Backend
### Backend Installation

```bash
cd backend

# تثبيت Node.js dependencies
npm install

# إنشاء Prisma client
npm run prisma:generate

# تشغيل migrations
npm run prisma:migrate

# بناء التطبيق
npm run build

# تشغيل التطبيق
npm start
```

### 3. تثبيت Frontend
### Frontend Installation

```bash
cd frontend

# تثبيت dependencies
npm install

# بناء التطبيق
npm run build

# تشغيل التطبيق
npm start
```

## الإعداد الأولي
## Initial Configuration

### 1. تسجيل الدخول الأولي
### First Login

- افتح المتصفح وانتقل إلى: `http://localhost:3000`
- استخدم بيانات المدير الافتراضية:
  - **Email**: <EMAIL>
  - **Password**: admin123

### 2. إضافة أجهزة MikroTik
### Adding MikroTik Devices

1. انتقل إلى صفحة "الأجهزة" (Devices)
2. اضغط على "إضافة جهاز جديد" (Add New Device)
3. أدخل المعلومات التالية:
   - **اسم الجهاز**: اسم وصفي للجهاز
   - **عنوان IP**: عنوان IP للجهاز
   - **نوع الجهاز**: Router, Switch, Access Point, etc.
   - **اسم المستخدم**: اسم مستخدم API
   - **كلمة المرور**: كلمة مرور API
   - **منفذ API**: 8728 (افتراضي)

### 3. إعداد التنبيهات
### Alert Configuration

1. انتقل إلى صفحة "الإعدادات" (Settings)
2. قم بتكوين:
   - **حدود التنبيهات**: CPU, Memory, Temperature
   - **إعدادات البريد الإلكتروني**: SMTP settings
   - **فترات المراقبة**: Monitoring intervals

## إعداد أجهزة MikroTik
## MikroTik Device Configuration

### 1. تفعيل API
### Enable API

```bash
# الاتصال بجهاز MikroTik عبر SSH أو Winbox
/ip service enable api

# تعيين منفذ API (اختياري)
/ip service set api port=8728
```

### 2. إنشاء مستخدم API
### Create API User

```bash
# إنشاء مجموعة مستخدمين للمراقبة
/user group add name=monitoring policy=api,read,winbox

# إنشاء مستخدم للمراقبة
/user add name=monitoring password=monitoring123 group=monitoring
```

### 3. إعداد الأمان
### Security Configuration

```bash
# تقييد الوصول للـ API حسب IP
/ip service set api address=***********00/32

# تفعيل SSL للـ API (اختياري)
/ip service enable api-ssl
/ip service set api-ssl port=8729
```

## استكشاف الأخطاء
## Troubleshooting

### مشاكل الاتصال
### Connection Issues

#### خطأ في الاتصال بقاعدة البيانات
#### Database Connection Error

```bash
# فحص حالة PostgreSQL
docker-compose logs postgres

# إعادة تشغيل قاعدة البيانات
docker-compose restart postgres
```

#### خطأ في الاتصال بـ Redis
#### Redis Connection Error

```bash
# فحص حالة Redis
docker-compose logs redis

# إعادة تشغيل Redis
docker-compose restart redis
```

### مشاكل أجهزة MikroTik
### MikroTik Device Issues

#### فشل الاتصال بالجهاز
#### Device Connection Failed

1. تحقق من إعدادات الشبكة
2. تأكد من تفعيل API
3. تحقق من بيانات المصادقة
4. فحص جدار الحماية

```bash
# اختبار الاتصال
ping ***********

# اختبار منفذ API
telnet *********** 8728
```

### مشاكل الأداء
### Performance Issues

#### استهلاك عالي للذاكرة
#### High Memory Usage

```bash
# مراقبة استهلاك الموارد
docker stats

# تقليل فترة المراقبة
# تحرير MONITORING_INTERVAL في .env
```

#### بطء في الاستجابة
#### Slow Response

```bash
# فحص سجلات التطبيق
docker-compose logs backend

# تحسين إعدادات قاعدة البيانات
# زيادة connection pool
```

## الصيانة
## Maintenance

### النسخ الاحتياطي
### Backup

```bash
# نسخ احتياطي لقاعدة البيانات
docker-compose exec postgres pg_dump -U postgres mikrotik_monitoring > backup.sql

# نسخ احتياطي للملفات
tar -czf backup.tar.gz backend/uploads backend/logs
```

### التحديث
### Updates

```bash
# إيقاف النظام
docker-compose down

# تحديث الكود
git pull origin main

# إعادة بناء الصور
docker-compose build

# تشغيل النظام
docker-compose up -d
```

### تنظيف البيانات القديمة
### Cleanup Old Data

```bash
# تنظيف المقاييس القديمة (يتم تلقائياً)
# يمكن تعديل فترة الاحتفاظ في الإعدادات

# تنظيف السجلات
docker-compose exec backend npm run cleanup
```

## الأمان
## Security

### إعدادات الأمان الموصى بها
### Recommended Security Settings

1. **تغيير كلمات المرور الافتراضية**
2. **استخدام HTTPS في الإنتاج**
3. **تقييد الوصول للشبكة**
4. **تفعيل جدار الحماية**
5. **تحديث النظام بانتظام**

### إعداد SSL/TLS
### SSL/TLS Configuration

```bash
# إنشاء شهادات SSL
mkdir ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem

# تحديث docker-compose.yml لاستخدام HTTPS
```

## الدعم
## Support

### الحصول على المساعدة
### Getting Help

- **الوثائق**: راجع ملف README.md
- **المشاكل**: افتح issue في GitHub
- **المجتمع**: انضم إلى مجتمع المطورين

### معلومات النظام
### System Information

```bash
# معلومات الإصدار
docker-compose exec backend npm run version

# معلومات النظام
docker-compose exec backend npm run system-info
```
