@echo off
echo.
echo MikroTik Monitoring System
echo ==========================
echo.

echo [INFO] Checking system requirements...

REM Check Docker
where docker >nul 2>nul
if %errorlevel% equ 0 (
    echo [OK] Docker is installed
    docker info >nul 2>nul
    if %errorlevel% equ 0 (
        echo [OK] Docker is running
        goto docker_start
    ) else (
        echo [WARNING] Docker is installed but not running
        echo Please start Docker Desktop first
        pause
        exit /b 1
    )
) else (
    echo [INFO] Docker not found, checking Node.js...
)

REM Check Node.js
where node >nul 2>nul
if %errorlevel% equ 0 (
    echo [OK] Node.js is installed
    goto node_start
) else (
    echo [ERROR] Neither Docker nor Node.js is installed
    echo.
    echo Please install one of the following:
    echo 1. Docker Desktop (recommended): https://www.docker.com/products/docker-desktop
    echo 2. Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:docker_start
echo.
echo Starting with Docker...
echo.

if not exist "docker-compose.yml" (
    echo [ERROR] docker-compose.yml not found
    echo Please make sure you are in the correct directory
    pause
    exit /b 1
)

echo [INFO] Starting services...
docker-compose up -d

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Services started successfully!
    echo.
    echo Frontend: http://localhost:3000
    echo Backend:  http://localhost:3001
    echo.
    echo Default login:
    echo Email: <EMAIL>
    echo Password: admin123
    echo.
    echo Opening browser...
    timeout /t 3 /nobreak >nul
    start http://localhost:3000
    echo.
    echo Press any key to view logs...
    pause >nul
    docker-compose logs -f
) else (
    echo [ERROR] Failed to start services
    pause
)
goto :eof

:node_start
echo.
echo Starting with Node.js (development mode)...
echo.

if not exist "package.json" (
    echo [ERROR] package.json not found
    echo Please make sure you are in the correct directory
    pause
    exit /b 1
)

echo [INFO] Installing dependencies...
call npm install

echo [INFO] Installing backend dependencies...
cd backend
call npm install
cd ..

echo [INFO] Installing frontend dependencies...
cd frontend
call npm install
cd ..

echo [INFO] Setting up database...
cd backend
call npm run prisma:generate
call npm run prisma:migrate
cd ..

echo.
echo [INFO] Starting development servers...
echo Backend: http://localhost:3001
echo Frontend: http://localhost:3000
echo.
echo Press Ctrl+C to stop all services
echo.

call npm run dev
goto :eof
