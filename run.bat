@echo off
setlocal enabledelayedexpansion

echo.
echo 🚀 MikroTik Monitoring System
echo =============================
echo.

REM Check if requirements are installed
set "has_docker=false"
set "has_node=false"
set "docker_running=false"

echo [INFO] Checking system requirements...

REM Check Docker
where docker >nul 2>nul
if %errorlevel% equ 0 (
    set "has_docker=true"
    docker info >nul 2>nul
    if %errorlevel% equ 0 (
        set "docker_running=true"
    )
)

REM Check Node.js
where node >nul 2>nul
if %errorlevel% equ 0 (
    set "has_node=true"
)

echo.
echo 📋 System Status:
echo ================

if "%has_docker%"=="true" (
    echo ✅ Docker: Installed
    if "%docker_running%"=="true" (
        echo ✅ Docker: Running
    ) else (
        echo ⚠️  Docker: Not running
    )
) else (
    echo ❌ Docker: Not installed
)

if "%has_node%"=="true" (
    echo ✅ Node.js: Installed
) else (
    echo ❌ Node.js: Not installed
)

echo.

REM Determine best option
if "%has_docker%"=="true" if "%docker_running%"=="true" (
    echo 🎯 Recommended: Docker deployment
    echo.
    echo Starting with Docker...
    echo This will automatically set up:
    echo - PostgreSQL database
    echo - Redis cache
    echo - Backend API
    echo - Frontend interface
    echo.
    
    REM Check if docker-compose.yml exists
    if exist "docker-compose.yml" (
        echo [INFO] Starting services with Docker Compose...
        docker-compose up -d
        
        if %errorlevel% equ 0 (
            echo.
            echo ✅ Services started successfully!
            echo.
            echo 🌐 Access the system:
            echo Frontend: http://localhost:3000
            echo Backend:  http://localhost:3001
            echo.
            echo 🔑 Default login:
            echo Email:    <EMAIL>
            echo Password: admin123
            echo.
            echo 📝 View logs: docker-compose logs -f
            echo 🛑 Stop system: docker-compose down
            echo.
            
            REM Wait a moment then open browser
            echo Opening browser in 5 seconds...
            timeout /t 5 /nobreak >nul
            start http://localhost:3000
            
            echo.
            echo Press any key to view logs, or Ctrl+C to exit...
            pause >nul
            docker-compose logs -f
        ) else (
            echo.
            echo ❌ Failed to start services
            echo Check the logs above for errors.
            echo.
            pause
        )
    ) else (
        echo ❌ docker-compose.yml not found
        echo Please make sure you're in the correct directory.
        pause
    )
    
) else if "%has_node%"=="true" (
    echo 🎯 Available: Development mode
    echo.
    echo ⚠️  Note: You'll need to set up PostgreSQL and Redis manually
    echo Or install Docker for automatic setup.
    echo.
    echo Choose an option:
    echo 1) Continue with development mode (manual setup required)
    echo 2) Install Docker for easier setup
    echo 3) Exit
    echo.
    set /p dev_choice="Enter your choice (1-3): "
    
    if "!dev_choice!"=="1" (
        echo.
        echo Starting development mode...
        echo.
        
        REM Check if package.json exists
        if exist "package.json" (
            echo [INFO] Installing dependencies...
            call npm install
            
            if exist "backend\package.json" (
                echo [INFO] Installing backend dependencies...
                cd backend
                call npm install
                cd ..
            )
            
            if exist "frontend\package.json" (
                echo [INFO] Installing frontend dependencies...
                cd frontend
                call npm install
                cd ..
            )
            
            echo.
            echo [INFO] Starting development servers...
            echo Backend: http://localhost:3001
            echo Frontend: http://localhost:3000
            echo.
            echo Press Ctrl+C to stop all services
            echo.
            
            call npm run dev
        ) else (
            echo ❌ package.json not found
            echo Please make sure you're in the correct directory.
            pause
        )
        
    ) else if "!dev_choice!"=="2" (
        echo.
        echo Opening Docker installation page...
        start https://www.docker.com/products/docker-desktop
        echo.
        echo Please install Docker Desktop and restart this script.
        pause
        
    ) else (
        echo Goodbye!
        exit /b 0
    )
    
) else (
    echo ❌ Missing requirements
    echo.
    echo To run MikroTik Monitoring System, you need either:
    echo.
    echo Option 1 (Recommended): Docker Desktop
    echo - Easy setup and management
    echo - Automatic database configuration
    echo - Download: https://www.docker.com/products/docker-desktop
    echo.
    echo Option 2 (Development): Node.js + PostgreSQL + Redis
    echo - More control over configuration
    echo - Requires manual database setup
    echo - Download Node.js: https://nodejs.org/
    echo.
    echo Would you like to:
    echo 1) Install Docker Desktop (recommended)
    echo 2) Install Node.js (development)
    echo 3) View detailed installation guide
    echo 4) Exit
    echo.
    set /p install_choice="Enter your choice (1-4): "
    
    if "!install_choice!"=="1" (
        echo Opening Docker installation page...
        start https://www.docker.com/products/docker-desktop
        echo.
        echo After installation:
        echo 1. Restart your computer
        echo 2. Start Docker Desktop
        echo 3. Run this script again
        pause
        
    ) else if "!install_choice!"=="2" (
        echo Opening Node.js installation page...
        start https://nodejs.org/
        echo.
        echo After installation:
        echo 1. Restart Command Prompt
        echo 2. Install PostgreSQL and Redis
        echo 3. Run this script again
        pause
        
    ) else if "!install_choice!"=="3" (
        echo Opening installation guide...
        if exist "INSTALL_REQUIREMENTS.md" (
            start notepad INSTALL_REQUIREMENTS.md
        ) else (
            echo Installation guide not found.
        )
        pause
        
    ) else (
        echo Goodbye!
        exit /b 0
    )
)

echo.
echo Script completed.
pause
