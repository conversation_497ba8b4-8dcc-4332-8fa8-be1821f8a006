<?php
// إنشاء المستخدمين للرصين
require_once 'config/db.php';

echo "<h2>إنشاء المستخدمين - الرصين</h2>";

try {
    // المستخدمين الافتراضيين
    $users = [
        ['admin', 'admin123', 'مدير', 'المدير العام للنظام'],
        ['engineering', 'eng123', 'هندسة', 'قسم الهندسة'],
        ['maintenance', 'maint123', 'صيانة', 'قسم الصيانة'],
        ['lab', 'lab123', 'مختبر', 'قسم المختبر'],
        ['user', 'user123', 'مستخدم', 'مستخدم عادي']
    ];
    
    $created = 0;
    foreach ($users as $user) {
        $hashed_password = password_hash($user[1], PASSWORD_ARGON2ID);
        
        $stmt = $conn->prepare("INSERT IGNORE INTO users (username, password, role, status, full_name, created_at) VALUES (?, ?, ?, 'نشط', ?, NOW())");
        $stmt->bind_param("ssss", $user[0], $hashed_password, $user[2], $user[3]);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            echo "<p>✓ تم إنشاء المستخدم: {$user[0]} / {$user[1]} ({$user[2]})</p>";
            $created++;
        } else {
            echo "<p>• المستخدم {$user[0]} موجود مسبقاً</p>";
        }
    }
    
    echo "<h3 style='color: green;'>تم إنشاء $created مستخدم جديد</h3>";
    
    // عرض جميع المستخدمين
    echo "<h3>المستخدمين الحاليين:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم المستخدم</th><th>الدور</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>";
    
    $result = $conn->query("SELECT username, role, status, created_at FROM users ORDER BY created_at DESC");
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['username']}</td>";
        echo "<td>{$row['role']}</td>";
        echo "<td>{$row['status']}</td>";
        echo "<td>{$row['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><a href='login.php'>تسجيل الدخول</a> | <a href='index.php'>الصفحة الرئيسية</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
