import nodemailer from 'nodemailer';
import { prisma } from '@/services/database';
import { logger } from '@/utils/logger';
import { config } from '@/config/config';
import { NotificationType, NotificationStatus } from '@prisma/client';

export interface NotificationData {
  userId: string;
  type: NotificationType;
  recipient: string;
  subject?: string;
  content: string;
  alertId?: string;
}

class NotificationService {
  private static instance: NotificationService;
  private emailTransporter: nodemailer.Transporter | null = null;

  private constructor() {
    this.initializeEmailTransporter();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private initializeEmailTransporter(): void {
    if (!config.features.enableEmailNotifications) {
      return;
    }

    try {
      this.emailTransporter = nodemailer.createTransport({
        host: config.email.smtp.host,
        port: config.email.smtp.port,
        secure: config.email.smtp.secure,
        auth: {
          user: config.email.smtp.user,
          pass: config.email.smtp.pass,
        },
      });

      logger.info('Email transporter initialized');
    } catch (error) {
      logger.error('Failed to initialize email transporter:', error);
    }
  }

  /**
   * Send alert notification
   */
  public async sendAlertNotification(user: any, alert: any): Promise<void> {
    try {
      const subject = `[${alert.severity}] ${alert.title} - ${alert.device.name}`;
      const content = this.generateAlertEmailContent(alert);

      // Send email notification
      if (config.features.enableEmailNotifications && user.email) {
        await this.sendEmailNotification({
          userId: user.id,
          type: 'EMAIL',
          recipient: user.email,
          subject,
          content,
          alertId: alert.id,
        });
      }

      // Send SMS notification for critical alerts
      if (config.features.enableSmsNotifications && alert.severity === 'CRITICAL' && user.phone) {
        await this.sendSmsNotification({
          userId: user.id,
          type: 'SMS',
          recipient: user.phone,
          content: `CRITICAL: ${alert.title} - ${alert.device.name}`,
          alertId: alert.id,
        });
      }

    } catch (error) {
      logger.error('Error sending alert notification:', error);
    }
  }

  /**
   * Send escalation notification
   */
  public async sendEscalationNotification(user: any, alert: any): Promise<void> {
    try {
      const subject = `[ESCALATION] ${alert.title} - ${alert.device.name}`;
      const content = this.generateEscalationEmailContent(alert);

      // Send email notification
      if (config.features.enableEmailNotifications && user.email) {
        await this.sendEmailNotification({
          userId: user.id,
          type: 'EMAIL',
          recipient: user.email,
          subject,
          content,
          alertId: alert.id,
        });
      }

      // Always send SMS for escalations if enabled
      if (config.features.enableSmsNotifications && user.phone) {
        await this.sendSmsNotification({
          userId: user.id,
          type: 'SMS',
          recipient: user.phone,
          content: `ESCALATION: ${alert.title} - ${alert.device.name}`,
          alertId: alert.id,
        });
      }

    } catch (error) {
      logger.error('Error sending escalation notification:', error);
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(data: NotificationData): Promise<void> {
    if (!this.emailTransporter) {
      logger.warn('Email transporter not available');
      return;
    }

    try {
      // Create notification record
      const notification = await prisma.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          recipient: data.recipient,
          subject: data.subject,
          content: data.content,
          alertId: data.alertId,
          status: 'PENDING',
        },
      });

      // Send email
      await this.emailTransporter.sendMail({
        from: config.email.from,
        to: data.recipient,
        subject: data.subject,
        html: data.content,
      });

      // Update notification status
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          status: 'SENT',
          sentAt: new Date(),
        },
      });

      logger.info(`Email notification sent to ${data.recipient}`);

    } catch (error) {
      logger.error('Error sending email notification:', error);

      // Update notification status to failed
      try {
        await prisma.notification.updateMany({
          where: {
            recipient: data.recipient,
            status: 'PENDING',
          },
          data: {
            status: 'FAILED',
          },
        });
      } catch (updateError) {
        logger.error('Error updating notification status:', updateError);
      }
    }
  }

  /**
   * Send SMS notification
   */
  private async sendSmsNotification(data: NotificationData): Promise<void> {
    if (!config.features.enableSmsNotifications) {
      return;
    }

    try {
      // Create notification record
      const notification = await prisma.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          recipient: data.recipient,
          content: data.content,
          alertId: data.alertId,
          status: 'PENDING',
        },
      });

      // TODO: Implement SMS sending using Twilio or other SMS service
      // For now, just log the SMS content
      logger.info(`SMS notification (not implemented): ${data.content} to ${data.recipient}`);

      // Update notification status
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          status: 'SENT',
          sentAt: new Date(),
        },
      });

    } catch (error) {
      logger.error('Error sending SMS notification:', error);
    }
  }

  /**
   * Generate alert email content
   */
  private generateAlertEmailContent(alert: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Alert Notification</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          .header { background-color: ${this.getSeverityColor(alert.severity)}; color: white; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
          .alert-info { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
          .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; }
          .severity-${alert.severity.toLowerCase()} { border-left: 4px solid ${this.getSeverityColor(alert.severity)}; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>🚨 Alert Notification</h2>
            <p><strong>${alert.title}</strong></p>
          </div>
          
          <div class="alert-info severity-${alert.severity.toLowerCase()}">
            <h3>Alert Details</h3>
            <p><strong>Device:</strong> ${alert.device.name} (${alert.device.ipAddress})</p>
            <p><strong>Severity:</strong> ${alert.severity}</p>
            <p><strong>Type:</strong> ${alert.type}</p>
            <p><strong>Message:</strong> ${alert.message}</p>
            <p><strong>Time:</strong> ${alert.createdAt.toLocaleString()}</p>
            ${alert.threshold ? `<p><strong>Threshold:</strong> ${alert.threshold}</p>` : ''}
            ${alert.currentValue ? `<p><strong>Current Value:</strong> ${alert.currentValue}</p>` : ''}
          </div>
          
          <div class="footer">
            <p>This is an automated notification from MikroTik Monitoring System.</p>
            <p>Please log in to the system to acknowledge or resolve this alert.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate escalation email content
   */
  private generateEscalationEmailContent(alert: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Alert Escalation</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          .header { background-color: #dc3545; color: white; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
          .alert-info { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #dc3545; }
          .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>🚨 ALERT ESCALATION</h2>
            <p><strong>${alert.title}</strong></p>
          </div>
          
          <div class="alert-info">
            <h3>⚠️ This alert has been escalated due to lack of acknowledgment</h3>
            <p><strong>Device:</strong> ${alert.device.name} (${alert.device.ipAddress})</p>
            <p><strong>Severity:</strong> ${alert.severity}</p>
            <p><strong>Type:</strong> ${alert.type}</p>
            <p><strong>Message:</strong> ${alert.message}</p>
            <p><strong>Original Time:</strong> ${alert.createdAt.toLocaleString()}</p>
            <p><strong>Duration:</strong> ${this.getAlertDuration(alert.createdAt)}</p>
            ${alert.threshold ? `<p><strong>Threshold:</strong> ${alert.threshold}</p>` : ''}
            ${alert.currentValue ? `<p><strong>Current Value:</strong> ${alert.currentValue}</p>` : ''}
          </div>
          
          <div class="footer">
            <p><strong>IMMEDIATE ACTION REQUIRED</strong></p>
            <p>This critical alert requires immediate attention. Please log in to the system to acknowledge and resolve this issue.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getSeverityColor(severity: string): string {
    switch (severity) {
      case 'CRITICAL': return '#dc3545';
      case 'HIGH': return '#fd7e14';
      case 'MEDIUM': return '#ffc107';
      case 'LOW': return '#28a745';
      default: return '#6c757d';
    }
  }

  private getAlertDuration(createdAt: Date): string {
    const now = new Date();
    const diff = now.getTime() - createdAt.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  }

  /**
   * Get notification statistics
   */
  public async getNotificationStatistics(): Promise<{
    total: number;
    sent: number;
    failed: number;
    pending: number;
    byType: { [key: string]: number };
  }> {
    try {
      const [
        total,
        sent,
        failed,
        pending,
        byType,
      ] = await Promise.all([
        prisma.notification.count(),
        prisma.notification.count({ where: { status: 'SENT' } }),
        prisma.notification.count({ where: { status: 'FAILED' } }),
        prisma.notification.count({ where: { status: 'PENDING' } }),
        prisma.notification.groupBy({
          by: ['type'],
          _count: { type: true },
        }),
      ]);

      const typeStats: { [key: string]: number } = {};
      byType.forEach(item => {
        typeStats[item.type] = item._count.type;
      });

      return {
        total,
        sent,
        failed,
        pending,
        byType: typeStats,
      };

    } catch (error) {
      logger.error('Error getting notification statistics:', error);
      throw error;
    }
  }
}

export const notificationService = NotificationService.getInstance();
export { NotificationService };
