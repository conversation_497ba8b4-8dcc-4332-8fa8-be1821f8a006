{"name": "mikrotik-monitoring-system", "version": "1.0.0", "description": "Complete monitoring system for MikroTik network devices", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules .next", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "setup": "npm run install:all && npm run setup:backend", "setup:backend": "cd backend && npm run prisma:generate && npm run prisma:migrate", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && npm run format", "format:frontend": "cd frontend && npm run format"}, "keywords": ["mikrotik", "network", "monitoring", "routeros", "dashboard", "alerts", "real-time"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/mikrotik-monitoring.git"}, "bugs": {"url": "https://github.com/your-repo/mikrotik-monitoring/issues"}, "homepage": "https://github.com/your-repo/mikrotik-monitoring#readme"}