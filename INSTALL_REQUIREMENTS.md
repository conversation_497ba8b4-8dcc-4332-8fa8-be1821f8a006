# 📦 تثبيت المتطلبات الأساسية
# Installing System Requirements

## 🚨 المتطلبات المفقودة
النظام يحتاج إلى تثبيت المتطلبات التالية:

### 1. Node.js (مطلوب)
**ما هو:** بيئة تشغيل JavaScript للخادم
**لماذا نحتاجه:** لتشغيل Backend و Frontend

**التثبيت:**
1. اذهب إلى: https://nodejs.org/
2. حمل النسخة LTS (الموصى بها)
3. شغل الملف المحمل واتبع التعليمات
4. أعد تشغيل Command Prompt

**التحقق من التثبيت:**
```bash
node --version
npm --version
```

### 2. Docker Desktop (موصى به بشدة)
**ما هو:** منصة لتشغيل التطبيقات في حاويات
**لماذا نحتاجه:** لتشغيل قاعدة البيانات والنظام بسهولة

**التثبيت:**
1. اذهب إلى: https://www.docker.com/products/docker-desktop
2. حمل Docker Desktop for Windows
3. شغل الملف المحمل واتبع التعليمات
4. أعد تشغيل الكمبيوتر
5. شغل Docker Desktop

**التحقق من التثبيت:**
```bash
docker --version
docker-compose --version
```

---

## 🎯 خيارات التشغيل

### الخيار 1: Docker (الأسهل - موصى به)
**المتطلبات:** Docker Desktop فقط
**المميزات:**
- ✅ تثبيت تلقائي لقاعدة البيانات
- ✅ إعداد تلقائي للشبكة
- ✅ سهولة الإدارة
- ✅ عزل كامل للنظام

**خطوات التشغيل:**
```bash
# بعد تثبيت Docker
docker-compose up -d
```

### الخيار 2: تثبيت يدوي (للتطوير)
**المتطلبات:** Node.js + PostgreSQL + Redis
**المميزات:**
- ✅ تحكم كامل في الإعدادات
- ✅ سهولة التطوير والتعديل
- ✅ أداء أفضل للتطوير

**متطلبات إضافية:**
- PostgreSQL 14+
- Redis 6+

---

## 🔧 تثبيت PostgreSQL (للتشغيل اليدوي)

### Windows:
1. اذهب إلى: https://www.postgresql.org/download/windows/
2. حمل PostgreSQL 14 أو أحدث
3. شغل الملف واتبع التعليمات
4. احفظ كلمة مرور المستخدم postgres

### إعداد قاعدة البيانات:
```sql
-- افتح pgAdmin أو psql
CREATE DATABASE mikrotik_monitoring;
CREATE USER monitoring WITH PASSWORD 'monitoring123';
GRANT ALL PRIVILEGES ON DATABASE mikrotik_monitoring TO monitoring;
```

---

## 🔧 تثبيت Redis (للتشغيل اليدوي)

### Windows:
1. اذهب إلى: https://github.com/microsoftarchive/redis/releases
2. حمل Redis-x64-3.0.504.msi
3. شغل الملف واتبع التعليمات

### أو استخدم WSL:
```bash
# في WSL Ubuntu
sudo apt update
sudo apt install redis-server
sudo service redis-server start
```

---

## ⚡ التشغيل السريع بعد التثبيت

### إذا ثبتت Docker:
```bash
# في مجلد المشروع
docker-compose up -d

# فتح المتصفح
start http://localhost:3000
```

### إذا ثبتت Node.js فقط:
```bash
# تثبيت المكتبات
npm install

# تثبيت مكتبات Backend
cd backend
npm install
cd ..

# تثبيت مكتبات Frontend  
cd frontend
npm install
cd ..

# تشغيل النظام
npm run dev
```

---

## 🐛 حل المشاكل

### مشكلة: "docker command not found"
**الحل:**
1. تأكد من تثبيت Docker Desktop
2. أعد تشغيل Command Prompt
3. تأكد من تشغيل Docker Desktop

### مشكلة: "node command not found"
**الحل:**
1. تأكد من تثبيت Node.js
2. أعد تشغيل Command Prompt
3. تحقق من PATH في متغيرات البيئة

### مشكلة: "Port already in use"
**الحل:**
```bash
# إيقاف العمليات على المنافذ
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# قتل العملية (استبدل PID بالرقم الفعلي)
taskkill /PID <PID> /F
```

---

## 📞 الدعم

إذا واجهت مشاكل في التثبيت:

1. **تأكد من صلاحيات المدير** عند التثبيت
2. **أعد تشغيل الكمبيوتر** بعد التثبيت
3. **تحقق من جدار الحماية** وبرامج الحماية
4. **استخدم Command Prompt كمدير** للتشغيل

---

## 🎯 الخطوة التالية

بعد تثبيت المتطلبات:

1. **أعد تشغيل Command Prompt**
2. **انتقل لمجلد المشروع**
3. **شغل الأمر المناسب:**
   - مع Docker: `docker-compose up -d`
   - بدون Docker: `npm run dev`

---

## 📋 ملخص المتطلبات

### للتشغيل مع Docker (موصى به):
- ✅ Docker Desktop
- ✅ 4GB RAM
- ✅ 10GB مساحة فارغة

### للتشغيل اليدوي:
- ✅ Node.js 18+
- ✅ PostgreSQL 14+
- ✅ Redis 6+
- ✅ 4GB RAM
- ✅ 5GB مساحة فارغة
