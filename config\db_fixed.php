<?php
// تعطيل عرض الأخطاء في الإنتاج
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Load environment variables
$env_path = __DIR__ . '/../.env';
if (!file_exists($env_path)) {
    error_log("ملف الإعدادات (.env) غير موجود في المسار: " . $env_path);
    die("خطأ في إعدادات النظام، يرجى التواصل مع المطور");
}

$env = parse_ini_file($env_path);
if ($env === false) {
    error_log("فشل في قراءة ملف الإعدادات (.env)");
    die("خطأ في إعدادات النظام، يرجى التواصل مع المطور");
}

// إعدادات قاعدة البيانات مع القيم الافتراضية
$host = $env['DB_HOST'] ?? 'localhost';
$user = $env['DB_USER'] ?? 'root';
$pass = $env['DB_PASS'] ?? '';
$db = $env['DB_NAME'] ?? 'rasseem';
$port = $env['DB_PORT'] ?? 3306;
$charset = $env['DB_CHARSET'] ?? 'utf8mb4';

// إعداد خيارات الاتصال
$options = [
    MYSQLI_OPT_CONNECT_TIMEOUT => 10,
    MYSQLI_OPT_READ_TIMEOUT => 30,
    MYSQLI_INIT_COMMAND => "SET NAMES {$charset} COLLATE {$charset}_unicode_ci"
];

try {
    // إنشاء اتصال قاعدة البيانات
    $conn = new mysqli($host, $user, $pass, $db, $port);
    
    // التحقق من الاتصال
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    // تعيين ترميز الأحرف
    if (!$conn->set_charset($charset)) {
        throw new Exception("فشل في تعيين ترميز الأحرف: " . $conn->error);
    }
    
    // تعيين المنطقة الزمنية
    $timezone = $env['DB_TIMEZONE'] ?? 'Asia/Baghdad';
    $conn->query("SET time_zone = '{$timezone}'");
    
    // تعيين وضع SQL الصارم
    $conn->query("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
    
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("خطأ في قاعدة البيانات: " . $e->getMessage());
    
    // عرض رسالة خطأ عامة للمستخدم
    die("حدث خطأ في النظام، يرجى المحاولة لاحقاً أو التواصل مع الدعم الفني");
}

// دالة لتنظيف الاتصال عند انتهاء السكريبت
register_shutdown_function(function() use ($conn) {
    if ($conn && !$conn->connect_error) {
        $conn->close();
    }
});

// دالة مساعدة لتنفيذ الاستعلامات بأمان
function executeQuery($conn, $query, $params = [], $types = '') {
    try {
        if (empty($params)) {
            $result = $conn->query($query);
            if ($result === false) {
                throw new Exception("فشل في تنفيذ الاستعلام: " . $conn->error);
            }
            return $result;
        } else {
            $stmt = $conn->prepare($query);
            if ($stmt === false) {
                throw new Exception("فشل في تحضير الاستعلام: " . $conn->error);
            }
            
            if (!empty($types) && !empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            if (!$stmt->execute()) {
                throw new Exception("فشل في تنفيذ الاستعلام: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        }
    } catch (Exception $e) {
        error_log("خطأ في الاستعلام: " . $e->getMessage() . " | الاستعلام: " . $query);
        throw $e;
    }
}

// دالة للتحقق من وجود الجداول المطلوبة
function checkRequiredTables($conn) {
    $required_tables = ['users', 'login_attempts', 'login_logs'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '{$table}'");
        if ($result->num_rows == 0) {
            $missing_tables[] = $table;
        }
    }
    
    if (!empty($missing_tables)) {
        error_log("الجداول المفقودة في قاعدة البيانات: " . implode(', ', $missing_tables));
        return false;
    }
    
    return true;
}

// التحقق من وجود الجداول المطلوبة
if (!checkRequiredTables($conn)) {
    error_log("بعض الجداول المطلوبة مفقودة في قاعدة البيانات");
}
?>
