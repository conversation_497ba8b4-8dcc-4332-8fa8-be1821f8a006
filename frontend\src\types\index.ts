// User types
export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'ADMIN' | 'MANAGER' | 'USER' | 'VIEWER';

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

// Device types
export interface Device {
  id: string;
  name: string;
  ipAddress: string;
  macAddress?: string;
  deviceType: DeviceType;
  model?: string;
  routerOsVersion?: string;
  location?: string;
  description?: string;
  isActive: boolean;
  lastSeen?: string;
  uptime?: string;
  apiPort: number;
  apiUsername: string;
  apiPassword: string;
  snmpCommunity?: string;
  serialNumber?: string;
  firmwareVersion?: string;
  createdAt: string;
  updatedAt: string;
  owner: User;
  ownerId: string;
  interfaces: Interface[];
  metrics: DeviceMetric[];
  alerts: Alert[];
}

export type DeviceType = 'ROUTER' | 'SWITCH' | 'ACCESS_POINT' | 'BRIDGE' | 'GATEWAY' | 'FIREWALL';

export interface CreateDeviceData {
  name: string;
  ipAddress: string;
  deviceType: DeviceType;
  apiUsername: string;
  apiPassword: string;
  apiPort?: number;
  model?: string;
  location?: string;
  description?: string;
  snmpCommunity?: string;
}

export interface UpdateDeviceData extends Partial<CreateDeviceData> {
  isActive?: boolean;
}

// Interface types
export interface Interface {
  id: string;
  name: string;
  type: InterfaceType;
  macAddress?: string;
  mtu?: number;
  isEnabled: boolean;
  speed?: string;
  duplex?: string;
  rxBytes: string;
  txBytes: string;
  rxPackets: string;
  txPackets: string;
  rxErrors: string;
  txErrors: string;
  createdAt: string;
  updatedAt: string;
  deviceId: string;
  interfaceMetrics: InterfaceMetric[];
}

export type InterfaceType = 'ETHERNET' | 'WIRELESS' | 'BRIDGE' | 'VLAN' | 'PPP' | 'TUNNEL' | 'LOOPBACK';

// Metrics types
export interface DeviceMetric {
  id: string;
  cpuUsage?: number;
  memoryUsage?: number;
  memoryTotal?: string;
  diskUsage?: number;
  diskTotal?: string;
  temperature?: number;
  voltage?: number;
  fanSpeed?: number;
  activeConnections?: number;
  totalConnections?: number;
  timestamp: string;
  deviceId: string;
}

export interface InterfaceMetric {
  id: string;
  rxRate: string;
  txRate: string;
  utilization?: number;
  signalStrength?: number;
  noiseFloor?: number;
  collisions: string;
  drops: string;
  timestamp: string;
  interfaceId: string;
}

// Alert types
export interface Alert {
  id: string;
  title: string;
  message: string;
  severity: AlertSeverity;
  type: AlertType;
  status: AlertStatus;
  threshold?: number;
  currentValue?: number;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  deviceId: string;
  device: Device;
  assignedToId?: string;
  assignedTo?: User;
  notifications: Notification[];
}

export type AlertSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
export type AlertType = 'DEVICE_DOWN' | 'HIGH_CPU' | 'HIGH_MEMORY' | 'HIGH_TEMPERATURE' | 'INTERFACE_DOWN' | 'HIGH_TRAFFIC' | 'SECURITY_BREACH' | 'CONFIGURATION_CHANGE';
export type AlertStatus = 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'SUPPRESSED';

export interface CreateAlertData {
  deviceId: string;
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  threshold?: number;
  currentValue?: number;
}

// Notification types
export interface Notification {
  id: string;
  type: NotificationType;
  recipient: string;
  subject?: string;
  content: string;
  status: NotificationStatus;
  sentAt?: string;
  createdAt: string;
  userId: string;
  user: User;
  alertId?: string;
  alert?: Alert;
}

export type NotificationType = 'EMAIL' | 'SMS' | 'PUSH' | 'WEBHOOK';
export type NotificationStatus = 'PENDING' | 'SENT' | 'FAILED' | 'CANCELLED';

// Dashboard types
export interface DashboardStats {
  totalDevices: number;
  onlineDevices: number;
  offlineDevices: number;
  totalAlerts: number;
  activeAlerts: number;
  criticalAlerts: number;
  totalUsers: number;
  systemUptime: string;
}

export interface DeviceStatus {
  deviceId: string;
  deviceName: string;
  status: 'online' | 'offline' | 'warning';
  lastSeen?: string;
  cpuUsage?: number;
  memoryUsage?: number;
  temperature?: number;
}

// Chart types
export interface ChartDataPoint {
  timestamp: string;
  value: number;
  label?: string;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  code?: string;
  timestamp?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
  timestamp?: string;
  path?: string;
  method?: string;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

// Table types
export interface TableColumn<T = any> {
  key: keyof T | string;
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
}

export interface TableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  selection?: {
    selectedRowKeys: string[];
    onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
  };
  onRowClick?: (record: T) => void;
}

// Filter types
export interface FilterOption {
  label: string;
  value: string | number;
  count?: number;
}

export interface Filter {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'text';
  options?: FilterOption[];
  value?: any;
  placeholder?: string;
}

// Socket types
export interface SocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface DeviceMetricsEvent {
  deviceId: string;
  metrics: DeviceMetric;
  timestamp: string;
}

export interface AlertEvent {
  alert: Alert;
  type: 'new' | 'updated' | 'resolved';
  timestamp: string;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  badge?: string | number;
  children?: NavItem[];
  permission?: UserRole[];
}

// Settings types
export interface Settings {
  id: string;
  key: string;
  value: string;
  description?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SystemSettings {
  systemName: string;
  monitoringInterval: number;
  alertsEmailEnabled: boolean;
  alertsSmsEnabled: boolean;
  metricsRetentionDays: number;
  dashboardRefreshInterval: number;
}

// Audit log types
export interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  userId: string;
  user: User;
}

// Export types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
  columns?: string[];
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  'data-testid'?: string;
}
