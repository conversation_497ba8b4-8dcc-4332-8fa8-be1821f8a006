import { Router } from 'express';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { prisma } from '@/services/database';
import { mikrotikService } from '@/services/mikrotik';
import { logger } from '@/utils/logger';

const router = Router();

// Get all devices
router.get('/', authenticateToken, async (_req, res) => {
  try {
    const devices = await prisma.device.findMany({
      include: {
        interfaces: true,
        alerts: {
          where: { status: 'ACTIVE' },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            alerts: { where: { status: 'ACTIVE' } }
          }
        }
      }
    });

    res.json(devices);
  } catch (error) {
    logger.error('Error fetching devices:', error);
    res.status(500).json({ error: 'Failed to fetch devices' });
  }
});

// Get device by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const device = await prisma.device.findUnique({
      where: { id: req.params['id'] },
      include: {
        interfaces: true,
        alerts: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        metrics: {
          orderBy: { timestamp: 'desc' },
          take: 100
        }
      }
    });

    if (!device) {
      return res.status(404).json({ error: 'Device not found' });
    }

    res.json(device);
  } catch (error) {
    logger.error('Error fetching device:', error);
    res.status(500).json({ error: 'Failed to fetch device' });
  }
});

// Create new device
router.post('/', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { name, ipAddress, username, password, apiPort, snmpCommunity, deviceType } = req.body;

    // Test connection first
    const testDevice = {
      id: 'test',
      name,
      ipAddress,
      username,
      password,
      apiPort: apiPort || 8728
    };
    const isConnected = await mikrotikService.testConnection(testDevice);

    if (!isConnected) {
      return res.status(400).json({ error: 'Cannot connect to device' });
    }

    const device = await prisma.device.create({
      data: {
        name,
        ipAddress,
        deviceType: deviceType || 'ROUTER',
        username,
        password,
        apiUsername: username,
        apiPassword: password,
        apiPort: apiPort || 8728,
        snmpCommunity,
        ownerId: req.user!.id,
        status: 'ONLINE'
      }
    });

    res.status(201).json(device);
  } catch (error) {
    logger.error('Error creating device:', error);
    res.status(500).json({ error: 'Failed to create device' });
  }
});

// Update device
router.put('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { name, ipAddress, username, password, apiPort, snmpCommunity, deviceType } = req.body;

    const device = await prisma.device.update({
      where: { id: req.params['id'] },
      data: {
        name,
        ipAddress,
        deviceType,
        username,
        password,
        apiUsername: username,
        apiPassword: password,
        apiPort,
        snmpCommunity,
        updatedAt: new Date()
      }
    });

    res.json(device);
  } catch (error) {
    logger.error('Error updating device:', error);
    res.status(500).json({ error: 'Failed to update device' });
  }
});

// Delete device
router.delete('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    await prisma.device.delete({
      where: { id: req.params['id'] }
    });

    res.status(204).send();
  } catch (error) {
    logger.error('Error deleting device:', error);
    res.status(500).json({ error: 'Failed to delete device' });
  }
});

// Test device connection
router.post('/:id/test', authenticateToken, async (req, res) => {
  try {
    const device = await prisma.device.findUnique({
      where: { id: req.params['id'] }
    });

    if (!device) {
      return res.status(404).json({ error: 'Device not found' });
    }

    const isConnected = await mikrotikService.testConnection(device);
    res.json({ connected: isConnected });
  } catch (error) {
    logger.error('Error testing device connection:', error);
    res.status(500).json({ error: 'Failed to test connection' });
  }
});

export default router;
