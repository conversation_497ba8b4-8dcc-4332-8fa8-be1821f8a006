import cron from 'node-cron';
import { prisma } from '@/services/database';
import { mikrotikService, MikroTikDevice } from '@/services/mikrotik';
import { alertService } from '@/services/alert';
import { socketService } from '@/services/socket';
import { logger } from '@/utils/logger';
import { config } from '@/config/config';

interface MonitoringMetrics {
  deviceId: string;
  cpuUsage?: number;
  memoryUsage?: number;
  memoryTotal?: number;
  diskUsage?: number;
  diskTotal?: number;
  temperature?: number;
  voltage?: number;
  activeConnections?: number;
  uptime?: string;
  interfaces: InterfaceMetrics[];
}

interface InterfaceMetrics {
  interfaceId: string;
  name: string;
  rxRate: number;
  txRate: number;
  utilization?: number;
  rxBytes: number;
  txBytes: number;
  rxPackets: number;
  txPackets: number;
  rxErrors: number;
  txErrors: number;
  isUp: boolean;
}

class MonitoringService {
  private static instance: MonitoringService;
  private monitoringTask: cron.ScheduledTask | null = null;
  private cleanupTask: cron.ScheduledTask | null = null;
  private isRunning = false;
  private lastMetrics: Map<string, any> = new Map();

  private constructor() {}

  public static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = MonitoringService.getInstance();
    await instance.start();
  }

  public static async stop(): Promise<void> {
    const instance = MonitoringService.getInstance();
    await instance.stop();
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Monitoring service is already running');
      return;
    }

    try {
      // Start monitoring task
      const intervalSeconds = Math.floor(config.monitoring.interval / 1000);
      this.monitoringTask = cron.schedule(`*/${intervalSeconds} * * * * *`, async () => {
        await this.monitorAllDevices();
      });

      // Start cleanup task (runs every hour)
      this.cleanupTask = cron.schedule('0 * * * *', async () => {
        await this.cleanupOldMetrics();
      });

      this.isRunning = true;
      logger.info(`Monitoring service started with ${intervalSeconds}s interval`);

      // Run initial monitoring
      await this.monitorAllDevices();

    } catch (error) {
      logger.error('Failed to start monitoring service:', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      if (this.monitoringTask) {
        this.monitoringTask.stop();
        this.monitoringTask = null;
      }

      if (this.cleanupTask) {
        this.cleanupTask.stop();
        this.cleanupTask = null;
      }

      // Disconnect all MikroTik connections
      await mikrotikService.disconnectAll();

      this.isRunning = false;
      logger.info('Monitoring service stopped');

    } catch (error) {
      logger.error('Error stopping monitoring service:', error);
      throw error;
    }
  }

  private async monitorAllDevices(): Promise<void> {
    try {
      // Get all active devices
      const devices = await prisma.device.findMany({
        where: { isActive: true },
        include: { interfaces: true },
      });

      if (devices.length === 0) {
        return;
      }

      logger.debug(`Monitoring ${devices.length} devices`);

      // Monitor devices in parallel with limited concurrency
      const concurrency = 5;
      const chunks = this.chunkArray(devices, concurrency);

      for (const chunk of chunks) {
        await Promise.allSettled(
          chunk.map(device => this.monitorDevice(device))
        );
      }

    } catch (error) {
      logger.error('Error monitoring devices:', error);
    }
  }

  private async monitorDevice(device: any): Promise<void> {
    const startTime = Date.now();
    
    try {
      const mikrotikDevice: MikroTikDevice = {
        id: device.id,
        name: device.name,
        ipAddress: device.ipAddress,
        apiPort: device.apiPort,
        username: device.apiUsername,
        password: device.apiPassword,
      };

      // Test connection first
      const isConnected = await mikrotikService.testConnection(mikrotikDevice);
      
      if (!isConnected) {
        await this.handleDeviceDown(device);
        return;
      }

      // Get device metrics
      const [systemResource, interfaces, systemHealth] = await Promise.all([
        mikrotikService.getSystemResource(mikrotikDevice),
        mikrotikService.getInterfaces(mikrotikDevice),
        mikrotikService.getSystemHealth(mikrotikDevice),
      ]);

      // Calculate metrics
      const metrics = await this.calculateMetrics(device, systemResource, interfaces, systemHealth);

      // Store metrics in database
      await this.storeMetrics(metrics);

      // Update device last seen
      await prisma.device.update({
        where: { id: device.id },
        data: { 
          lastSeen: new Date(),
          uptime: this.parseUptime(systemResource.uptime),
        },
      });

      // Check for alerts
      await this.checkAlerts(device, metrics);

      // Emit real-time data
      socketService.emitDeviceMetrics(device.id, metrics);

      const duration = Date.now() - startTime;
      logger.debug(`Monitored device ${device.name} in ${duration}ms`);

    } catch (error) {
      logger.error(`Error monitoring device ${device.name}:`, error);
      await this.handleDeviceError(device, error);
    }
  }

  private async calculateMetrics(
    device: any,
    systemResource: any,
    interfaces: any[],
    systemHealth: any
  ): Promise<MonitoringMetrics> {
    const deviceKey = device.id;
    const lastMetrics = this.lastMetrics.get(deviceKey);
    const currentTime = Date.now();

    // Calculate CPU and memory usage
    const cpuUsage = systemResource.cpuLoad;
    const memoryUsage = systemResource.totalMemory > 0 
      ? ((systemResource.totalMemory - systemResource.freeMemory) / systemResource.totalMemory) * 100 
      : 0;
    const diskUsage = systemResource.totalDiskSpace > 0 
      ? ((systemResource.totalDiskSpace - systemResource.freeDiskSpace) / systemResource.totalDiskSpace) * 100 
      : 0;

    // Calculate interface metrics
    const interfaceMetrics: InterfaceMetrics[] = [];
    
    for (const iface of interfaces) {
      const dbInterface = device.interfaces.find((i: any) => i.name === iface.name);
      if (!dbInterface) continue;

      let rxRate = 0;
      let txRate = 0;

      // Calculate rates if we have previous metrics
      if (lastMetrics && lastMetrics.interfaces) {
        const lastInterface = lastMetrics.interfaces.find((i: any) => i.name === iface.name);
        if (lastInterface && lastMetrics.timestamp) {
          const timeDiff = (currentTime - lastMetrics.timestamp) / 1000; // seconds
          const rxBytesDiff = iface.rxByte - lastInterface.rxBytes;
          const txBytesDiff = iface.txByte - lastInterface.txBytes;
          
          rxRate = timeDiff > 0 ? Math.max(0, rxBytesDiff / timeDiff) : 0;
          txRate = timeDiff > 0 ? Math.max(0, txBytesDiff / timeDiff) : 0;
        }
      }

      interfaceMetrics.push({
        interfaceId: dbInterface.id,
        name: iface.name,
        rxRate,
        txRate,
        rxBytes: iface.rxByte,
        txBytes: iface.txByte,
        rxPackets: iface.rxPacket,
        txPackets: iface.txPacket,
        rxErrors: iface.rxError,
        txErrors: iface.txError,
        isUp: iface.running && !iface.disabled,
      });
    }

    const metrics: MonitoringMetrics = {
      deviceId: device.id,
      cpuUsage,
      memoryUsage,
      memoryTotal: systemResource.totalMemory,
      diskUsage,
      diskTotal: systemResource.totalDiskSpace,
      temperature: systemHealth?.temperature,
      voltage: systemHealth?.voltage,
      uptime: systemResource.uptime,
      interfaces: interfaceMetrics,
    };

    // Store current metrics for next calculation
    this.lastMetrics.set(deviceKey, {
      ...metrics,
      timestamp: currentTime,
    });

    return metrics;
  }

  private async storeMetrics(metrics: MonitoringMetrics): Promise<void> {
    try {
      // Store device metrics
      await prisma.deviceMetric.create({
        data: {
          deviceId: metrics.deviceId,
          cpuUsage: metrics.cpuUsage,
          memoryUsage: metrics.memoryUsage,
          memoryTotal: metrics.memoryTotal ? BigInt(metrics.memoryTotal) : null,
          diskUsage: metrics.diskUsage,
          diskTotal: metrics.diskTotal ? BigInt(metrics.diskTotal) : null,
          temperature: metrics.temperature,
          voltage: metrics.voltage,
          activeConnections: metrics.activeConnections,
        },
      });

      // Store interface metrics
      for (const interfaceMetric of metrics.interfaces) {
        await prisma.interfaceMetric.create({
          data: {
            interfaceId: interfaceMetric.interfaceId,
            rxRate: BigInt(Math.round(interfaceMetric.rxRate)),
            txRate: BigInt(Math.round(interfaceMetric.txRate)),
            utilization: interfaceMetric.utilization,
          },
        });

        // Update interface statistics
        await prisma.interface.update({
          where: { id: interfaceMetric.interfaceId },
          data: {
            rxBytes: BigInt(interfaceMetric.rxBytes),
            txBytes: BigInt(interfaceMetric.txBytes),
            rxPackets: BigInt(interfaceMetric.rxPackets),
            txPackets: BigInt(interfaceMetric.txPackets),
            rxErrors: BigInt(interfaceMetric.rxErrors),
            txErrors: BigInt(interfaceMetric.txErrors),
          },
        });
      }

    } catch (error) {
      logger.error('Error storing metrics:', error);
      throw error;
    }
  }

  private async checkAlerts(device: any, metrics: MonitoringMetrics): Promise<void> {
    try {
      const alerts = [];

      // Check CPU usage
      if (metrics.cpuUsage && metrics.cpuUsage > 80) {
        alerts.push({
          type: 'HIGH_CPU',
          severity: metrics.cpuUsage > 95 ? 'CRITICAL' : 'HIGH',
          title: 'High CPU Usage',
          message: `CPU usage is ${metrics.cpuUsage.toFixed(1)}%`,
          threshold: 80,
          currentValue: metrics.cpuUsage,
        });
      }

      // Check memory usage
      if (metrics.memoryUsage && metrics.memoryUsage > 85) {
        alerts.push({
          type: 'HIGH_MEMORY',
          severity: metrics.memoryUsage > 95 ? 'CRITICAL' : 'HIGH',
          title: 'High Memory Usage',
          message: `Memory usage is ${metrics.memoryUsage.toFixed(1)}%`,
          threshold: 85,
          currentValue: metrics.memoryUsage,
        });
      }

      // Check temperature
      if (metrics.temperature && metrics.temperature > 70) {
        alerts.push({
          type: 'HIGH_TEMPERATURE',
          severity: metrics.temperature > 80 ? 'CRITICAL' : 'HIGH',
          title: 'High Temperature',
          message: `Device temperature is ${metrics.temperature}°C`,
          threshold: 70,
          currentValue: metrics.temperature,
        });
      }

      // Check interface status
      for (const iface of metrics.interfaces) {
        if (!iface.isUp) {
          alerts.push({
            type: 'INTERFACE_DOWN',
            severity: 'MEDIUM',
            title: 'Interface Down',
            message: `Interface ${iface.name} is down`,
            threshold: null,
            currentValue: null,
          });
        }
      }

      // Create alerts
      for (const alert of alerts) {
        await alertService.createAlert({
          deviceId: device.id,
          type: alert.type,
          severity: alert.severity,
          title: alert.title,
          message: alert.message,
          threshold: alert.threshold,
          currentValue: alert.currentValue,
        });
      }

    } catch (error) {
      logger.error('Error checking alerts:', error);
    }
  }

  private async handleDeviceDown(device: any): Promise<void> {
    try {
      logger.warn(`Device ${device.name} is down`);

      // Create device down alert
      await alertService.createAlert({
        deviceId: device.id,
        type: 'DEVICE_DOWN',
        severity: 'CRITICAL',
        title: 'Device Down',
        message: `Device ${device.name} is not responding`,
        threshold: null,
        currentValue: null,
      });

      // Emit device down event
      socketService.emitDeviceDown(device.id);

    } catch (error) {
      logger.error('Error handling device down:', error);
    }
  }

  private async handleDeviceError(device: any, error: any): Promise<void> {
    try {
      logger.error(`Device ${device.name} error:`, error);

      // Create device error alert
      await alertService.createAlert({
        deviceId: device.id,
        type: 'DEVICE_DOWN',
        severity: 'HIGH',
        title: 'Device Error',
        message: `Error monitoring device ${device.name}: ${error.message}`,
        threshold: null,
        currentValue: null,
      });

    } catch (alertError) {
      logger.error('Error handling device error:', alertError);
    }
  }

  private async cleanupOldMetrics(): Promise<void> {
    try {
      const retentionDays = await this.getMetricsRetentionDays();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const [deviceMetricsDeleted, interfaceMetricsDeleted] = await Promise.all([
        prisma.deviceMetric.deleteMany({
          where: {
            timestamp: {
              lt: cutoffDate,
            },
          },
        }),
        prisma.interfaceMetric.deleteMany({
          where: {
            timestamp: {
              lt: cutoffDate,
            },
          },
        }),
      ]);

      logger.info(
        `Cleaned up old metrics: ${deviceMetricsDeleted.count} device metrics, ${interfaceMetricsDeleted.count} interface metrics`
      );

    } catch (error) {
      logger.error('Error cleaning up old metrics:', error);
    }
  }

  private async getMetricsRetentionDays(): Promise<number> {
    try {
      const setting = await prisma.setting.findUnique({
        where: { key: 'metrics.retention.days' },
      });
      return setting ? parseInt(setting.value) : 30;
    } catch (error) {
      logger.error('Error getting metrics retention setting:', error);
      return 30;
    }
  }

  private parseUptime(uptime: string): bigint {
    // Parse uptime string like "1w2d3h4m5s" to seconds
    const regex = /(?:(\d+)w)?(?:(\d+)d)?(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?/;
    const match = uptime.match(regex);

    if (!match) return BigInt(0);

    const weeks = parseInt(match[1] || '0');
    const days = parseInt(match[2] || '0');
    const hours = parseInt(match[3] || '0');
    const minutes = parseInt(match[4] || '0');
    const seconds = parseInt(match[5] || '0');

    const totalSeconds = weeks * 7 * 24 * 3600 + days * 24 * 3600 + hours * 3600 + minutes * 60 + seconds;
    return BigInt(totalSeconds);
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  public getStatus(): { isRunning: boolean; devicesMonitored: number } {
    return {
      isRunning: this.isRunning,
      devicesMonitored: this.lastMetrics.size,
    };
  }
}

export const monitoringService = MonitoringService.getInstance();
export { MonitoringService };
