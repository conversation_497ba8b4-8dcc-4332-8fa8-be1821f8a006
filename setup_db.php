<?php
// إعداد قاعدة البيانات للرصين
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>إعداد قاعدة البيانات - الرصين</h2>";

// الاتصال بقاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✓ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS rasseem CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ تم إنشاء قاعدة البيانات rasseem</p>";
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO("mysql:host=$host;dbname=rasseem;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة ملف SQL
    $sql_file = 'database_setup.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    $executed = 0;
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^--/', $query)) {
            try {
                $pdo->exec($query);
                $executed++;
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "<p style='color: orange;'>تحذير: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        }
    }
    
    echo "<p>✓ تم تنفيذ $executed استعلام SQL</p>";
    
    // إنشاء مستخدم إداري افتراضي
    $admin_password = password_hash('admin123', PASSWORD_ARGON2ID);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, role, status, created_at) VALUES (?, ?, 'مدير', 'نشط', NOW())");
    $stmt->execute(['admin', $admin_password]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p>✓ تم إنشاء المستخدم الإداري: admin / admin123</p>";
    } else {
        echo "<p>• المستخدم الإداري موجود مسبقاً</p>";
    }
    
    echo "<h3 style='color: green;'>✓ تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p><a href='index.php'>الانتقال للصفحة الرئيسية</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
