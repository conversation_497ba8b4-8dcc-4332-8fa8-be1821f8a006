import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { config } from '@/config/config';

class DatabaseService {
  private static instance: DatabaseService;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
      errorFormat: 'pretty',
    });

    this.setupEventListeners();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = DatabaseService.getInstance();
    await instance.connect();
  }

  public static async disconnect(): Promise<void> {
    const instance = DatabaseService.getInstance();
    await instance.disconnect();
  }

  public static getClient(): PrismaClient {
    const instance = DatabaseService.getInstance();
    return instance.prisma;
  }

  private setupEventListeners(): void {
    // Log database queries in development
    if (config.nodeEnv === 'development') {
      this.prisma.$on('query', (e) => {
        logger.debug(`Query: ${e.query}`);
        logger.debug(`Params: ${e.params}`);
        logger.debug(`Duration: ${e.duration}ms`);
      });
    }

    // Log database errors
    this.prisma.$on('error', (e) => {
      logger.error('Database error:', e);
    });

    // Log database info
    this.prisma.$on('info', (e) => {
      logger.info(`Database info: ${e.message}`);
    });

    // Log database warnings
    this.prisma.$on('warn', (e) => {
      logger.warn(`Database warning: ${e.message}`);
    });
  }

  private async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      logger.info('Database connected successfully');

      // Test the connection
      await this.prisma.$queryRaw`SELECT 1`;
      logger.info('Database connection test passed');

      // Run initial setup
      await this.initialSetup();

    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  private async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from database:', error);
      throw error;
    }
  }

  private async initialSetup(): Promise<void> {
    try {
      // Create default admin user if it doesn't exist
      await this.createDefaultAdmin();

      // Create default settings
      await this.createDefaultSettings();

      logger.info('Database initial setup completed');
    } catch (error) {
      logger.error('Error during database initial setup:', error);
      throw error;
    }
  }

  private async createDefaultAdmin(): Promise<void> {
    try {
      const existingAdmin = await this.prisma.user.findFirst({
        where: { role: 'ADMIN' },
      });

      if (!existingAdmin) {
        const bcrypt = await import('bcryptjs');
        const hashedPassword = await bcrypt.hash(
          config.defaultAdmin.password,
          config.security.bcryptRounds
        );

        await this.prisma.user.create({
          data: {
            email: config.defaultAdmin.email,
            username: config.defaultAdmin.username,
            password: hashedPassword,
            firstName: 'System',
            lastName: 'Administrator',
            role: 'ADMIN',
            isActive: true,
          },
        });

        logger.info('Default admin user created');
      }
    } catch (error) {
      logger.error('Error creating default admin user:', error);
      throw error;
    }
  }

  private async createDefaultSettings(): Promise<void> {
    try {
      const defaultSettings = [
        {
          key: 'system.name',
          value: 'MikroTik Monitoring System',
          description: 'System name displayed in the interface',
          category: 'system',
          type: 'STRING',
        },
        {
          key: 'monitoring.interval',
          value: config.monitoring.interval.toString(),
          description: 'Monitoring interval in milliseconds',
          category: 'monitoring',
          type: 'NUMBER',
        },
        {
          key: 'alerts.email.enabled',
          value: config.features.enableEmailNotifications.toString(),
          description: 'Enable email notifications for alerts',
          category: 'alerts',
          type: 'BOOLEAN',
        },
        {
          key: 'alerts.sms.enabled',
          value: config.features.enableSmsNotifications.toString(),
          description: 'Enable SMS notifications for alerts',
          category: 'alerts',
          type: 'BOOLEAN',
        },
        {
          key: 'metrics.retention.days',
          value: '30',
          description: 'Number of days to retain metrics data',
          category: 'metrics',
          type: 'NUMBER',
        },
        {
          key: 'dashboard.refresh.interval',
          value: '30000',
          description: 'Dashboard refresh interval in milliseconds',
          category: 'dashboard',
          type: 'NUMBER',
        },
      ];

      for (const setting of defaultSettings) {
        await this.prisma.setting.upsert({
          where: { key: setting.key },
          update: {},
          create: setting,
        });
      }

      logger.info('Default settings created/updated');
    } catch (error) {
      logger.error('Error creating default settings:', error);
      throw error;
    }
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Transaction wrapper
  public async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>
  ): Promise<T> {
    return this.prisma.$transaction(fn);
  }

  // Cleanup old metrics
  public async cleanupOldMetrics(retentionDays: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const [deviceMetricsDeleted, interfaceMetricsDeleted] = await Promise.all([
        this.prisma.deviceMetric.deleteMany({
          where: {
            timestamp: {
              lt: cutoffDate,
            },
          },
        }),
        this.prisma.interfaceMetric.deleteMany({
          where: {
            timestamp: {
              lt: cutoffDate,
            },
          },
        }),
      ]);

      logger.info(
        `Cleaned up old metrics: ${deviceMetricsDeleted.count} device metrics, ${interfaceMetricsDeleted.count} interface metrics`
      );
    } catch (error) {
      logger.error('Error cleaning up old metrics:', error);
      throw error;
    }
  }

  // Get database statistics
  public async getStatistics(): Promise<{
    users: number;
    devices: number;
    activeDevices: number;
    alerts: number;
    activeAlerts: number;
    deviceMetrics: number;
    interfaceMetrics: number;
  }> {
    try {
      const [
        users,
        devices,
        activeDevices,
        alerts,
        activeAlerts,
        deviceMetrics,
        interfaceMetrics,
      ] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.device.count(),
        this.prisma.device.count({ where: { isActive: true } }),
        this.prisma.alert.count(),
        this.prisma.alert.count({ where: { status: 'ACTIVE' } }),
        this.prisma.deviceMetric.count(),
        this.prisma.interfaceMetric.count(),
      ]);

      return {
        users,
        devices,
        activeDevices,
        alerts,
        activeAlerts,
        deviceMetrics,
        interfaceMetrics,
      };
    } catch (error) {
      logger.error('Error getting database statistics:', error);
      throw error;
    }
  }
}

export { DatabaseService };
export const prisma = DatabaseService.getClient();
