import { Router } from 'express';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { prisma } from '@/services/database';
import { logger } from '@/utils/logger';

const router = Router();

// Get all alerts
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { status, severity, deviceId, page = 1, limit = 20 } = req.query;
    
    const where: any = {};
    if (status) where.status = status;
    if (severity) where.severity = severity;
    if (deviceId) where.deviceId = deviceId;

    const alerts = await prisma.alert.findMany({
      where,
      include: {
        device: {
          select: { id: true, name: true, ipAddress: true }
        },
        assignedTo: {
          select: { id: true, username: true, email: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit)
    });

    const total = await prisma.alert.count({ where });

    res.json({
      alerts,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    logger.error('Error fetching alerts:', error);
    res.status(500).json({ error: 'Failed to fetch alerts' });
  }
});

// Get alert by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const alert = await prisma.alert.findUnique({
      where: { id: req.params['id'] },
      include: {
        device: true,
        assignedTo: {
          select: { id: true, username: true, email: true }
        }
      }
    });

    if (!alert) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json(alert);
  } catch (error) {
    logger.error('Error fetching alert:', error);
    res.status(500).json({ error: 'Failed to fetch alert' });
  }
});

// Update alert status
router.patch('/:id/status', authenticateToken, async (req, res) => {
  try {
    const { status } = req.body;

    if (!['ACTIVE', 'ACKNOWLEDGED', 'RESOLVED'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const alert = await prisma.alert.update({
      where: { id: req.params['id'] },
      data: {
        status,
        ...(status === 'ACKNOWLEDGED' && { acknowledgedAt: new Date() }),
        ...(status === 'RESOLVED' && { resolvedAt: new Date() }),
        updatedAt: new Date()
      },
      include: {
        device: true,
        assignedTo: {
          select: { id: true, username: true, email: true }
        }
      }
    });

    res.json(alert);
  } catch (error) {
    logger.error('Error updating alert status:', error);
    res.status(500).json({ error: 'Failed to update alert status' });
  }
});

// Assign alert to user
router.patch('/:id/assign', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { assignedToId } = req.body;

    const alert = await prisma.alert.update({
      where: { id: req.params['id'] },
      data: {
        assignedToId,
        updatedAt: new Date()
      },
      include: {
        device: true,
        assignedTo: {
          select: { id: true, username: true, email: true }
        }
      }
    });

    res.json(alert);
  } catch (error) {
    logger.error('Error assigning alert:', error);
    res.status(500).json({ error: 'Failed to assign alert' });
  }
});

// Delete alert
router.delete('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    await prisma.alert.delete({
      where: { id: req.params['id'] }
    });

    res.status(204).send();
  } catch (error) {
    logger.error('Error deleting alert:', error);
    res.status(500).json({ error: 'Failed to delete alert' });
  }
});

// Get alert statistics
router.get('/stats/summary', authenticateToken, async (_req, res) => {
  try {
    const stats = await prisma.alert.groupBy({
      by: ['status', 'severity'],
      _count: true
    });

    const summary = {
      total: 0,
      active: 0,
      acknowledged: 0,
      resolved: 0,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    };

    stats.forEach(stat => {
      summary.total += stat._count;
      
      if (stat.status === 'ACTIVE') summary.active += stat._count;
      else if (stat.status === 'ACKNOWLEDGED') summary.acknowledged += stat._count;
      else if (stat.status === 'RESOLVED') summary.resolved += stat._count;

      if (stat.severity === 'CRITICAL') summary.critical += stat._count;
      else if (stat.severity === 'HIGH') summary.high += stat._count;
      else if (stat.severity === 'MEDIUM') summary.medium += stat._count;
      else if (stat.severity === 'LOW') summary.low += stat._count;
    });

    res.json(summary);
  } catch (error) {
    logger.error('Error fetching alert statistics:', error);
    res.status(500).json({ error: 'Failed to fetch alert statistics' });
  }
});

export default router;
