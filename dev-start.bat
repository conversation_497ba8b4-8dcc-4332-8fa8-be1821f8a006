@echo off
echo 🚀 Starting MikroTik Monitoring System - Development Mode
echo ========================================================

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ first.
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

REM Install root dependencies if needed
if not exist "node_modules" (
    echo [INFO] Installing root dependencies...
    call npm install
)

REM Install backend dependencies if needed
if not exist "backend\node_modules" (
    echo [INFO] Installing backend dependencies...
    cd backend
    call npm install
    cd ..
)

REM Install frontend dependencies if needed
if not exist "frontend\node_modules" (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    call npm install
    cd ..
)

REM Setup backend database if needed
echo [INFO] Setting up backend database...
cd backend
call npm run prisma:generate
call npm run prisma:migrate
cd ..

echo.
echo [SUCCESS] Setup complete!
echo.
echo [INFO] Starting development servers...
echo [INFO] Backend will run on: http://localhost:3001
echo [INFO] Frontend will run on: http://localhost:3000
echo.
echo [WARNING] Press Ctrl+C to stop all services
echo.

REM Start both backend and frontend
call npm run dev
