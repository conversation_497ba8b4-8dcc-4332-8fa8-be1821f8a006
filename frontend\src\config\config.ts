// Environment configuration
export const config = {
  // API Configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1',
    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
  },

  // WebSocket Configuration
  websocket: {
    url: process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3001',
    reconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS || '5'),
    reconnectInterval: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_INTERVAL || '3000'),
  },

  // App Configuration
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'MikroTik Monitoring System',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    description: process.env.NEXT_PUBLIC_APP_DESCRIPTION || 'Network monitoring system for MikroTik devices',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  },

  // Authentication Configuration
  auth: {
    tokenKey: 'mikrotik_monitoring_token',
    refreshTokenKey: 'mikrotik_monitoring_refresh_token',
    userKey: 'mikrotik_monitoring_user',
    sessionTimeout: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'), // 1 hour
  },

  // UI Configuration
  ui: {
    theme: {
      default: 'light',
      storageKey: 'mikrotik_monitoring_theme',
    },
    sidebar: {
      storageKey: 'mikrotik_monitoring_sidebar_collapsed',
    },
    dashboard: {
      refreshInterval: parseInt(process.env.NEXT_PUBLIC_DASHBOARD_REFRESH_INTERVAL || '30000'), // 30 seconds
      maxDataPoints: parseInt(process.env.NEXT_PUBLIC_MAX_DATA_POINTS || '100'),
    },
    notifications: {
      position: 'top-right' as const,
      duration: parseInt(process.env.NEXT_PUBLIC_NOTIFICATION_DURATION || '5000'),
    },
    pagination: {
      defaultPageSize: parseInt(process.env.NEXT_PUBLIC_DEFAULT_PAGE_SIZE || '20'),
      pageSizeOptions: [10, 20, 50, 100],
    },
  },

  // Feature Flags
  features: {
    darkMode: process.env.NEXT_PUBLIC_ENABLE_DARK_MODE !== 'false',
    notifications: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS !== 'false',
    realTimeUpdates: process.env.NEXT_PUBLIC_ENABLE_REAL_TIME !== 'false',
    exportData: process.env.NEXT_PUBLIC_ENABLE_EXPORT !== 'false',
    advancedFilters: process.env.NEXT_PUBLIC_ENABLE_ADVANCED_FILTERS !== 'false',
    deviceMap: process.env.NEXT_PUBLIC_ENABLE_DEVICE_MAP !== 'false',
  },

  // Monitoring Configuration
  monitoring: {
    alertSeverityColors: {
      LOW: '#10b981', // green-500
      MEDIUM: '#f59e0b', // amber-500
      HIGH: '#f97316', // orange-500
      CRITICAL: '#ef4444', // red-500
    },
    deviceStatusColors: {
      online: '#10b981', // green-500
      offline: '#ef4444', // red-500
      warning: '#f59e0b', // amber-500
      unknown: '#6b7280', // gray-500
    },
    chartColors: [
      '#3b82f6', // blue-500
      '#10b981', // green-500
      '#f59e0b', // amber-500
      '#ef4444', // red-500
      '#8b5cf6', // violet-500
      '#f97316', // orange-500
      '#06b6d4', // cyan-500
      '#84cc16', // lime-500
    ],
  },

  // Validation Rules
  validation: {
    password: {
      minLength: 6,
      requireUppercase: false,
      requireLowercase: false,
      requireNumbers: false,
      requireSpecialChars: false,
    },
    username: {
      minLength: 3,
      maxLength: 30,
      allowedChars: /^[a-zA-Z0-9_]+$/,
    },
    deviceName: {
      minLength: 1,
      maxLength: 100,
    },
    ipAddress: {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    },
  },

  // Date and Time Configuration
  dateTime: {
    defaultFormat: 'yyyy-MM-dd HH:mm:ss',
    shortFormat: 'MMM dd, yyyy',
    timeFormat: 'HH:mm:ss',
    timezone: process.env.NEXT_PUBLIC_TIMEZONE || 'UTC',
    locale: process.env.NEXT_PUBLIC_LOCALE || 'en-US',
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '5242880'), // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  },

  // Development Configuration
  development: {
    enableDebugLogs: process.env.NODE_ENV === 'development',
    enableMockData: process.env.NEXT_PUBLIC_ENABLE_MOCK_DATA === 'true',
    showDevTools: process.env.NODE_ENV === 'development',
  },
};

// Type definitions
export type Config = typeof config;

export type AlertSeverity = keyof typeof config.monitoring.alertSeverityColors;
export type DeviceStatus = keyof typeof config.monitoring.deviceStatusColors;

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  return `${config.api.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

export const getWebSocketUrl = (): string => {
  return config.websocket.url;
};

export const isFeatureEnabled = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

export const getAlertSeverityColor = (severity: AlertSeverity): string => {
  return config.monitoring.alertSeverityColors[severity];
};

export const getDeviceStatusColor = (status: DeviceStatus): string => {
  return config.monitoring.deviceStatusColors[status];
};

export const formatDate = (date: Date | string, format?: string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Simple date formatting (in a real app, you'd use a library like date-fns)
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: config.dateTime.timezone,
  };

  return dateObj.toLocaleDateString(config.dateTime.locale, options);
};

export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const rules = config.validation.password;

  if (password.length < rules.minLength) {
    errors.push(`Password must be at least ${rules.minLength} characters long`);
  }

  if (rules.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (rules.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (rules.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (rules.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const validateIpAddress = (ip: string): boolean => {
  return config.validation.ipAddress.pattern.test(ip);
};

export const validateUsername = (username: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const rules = config.validation.username;

  if (username.length < rules.minLength) {
    errors.push(`Username must be at least ${rules.minLength} characters long`);
  }

  if (username.length > rules.maxLength) {
    errors.push(`Username must be no more than ${rules.maxLength} characters long`);
  }

  if (!rules.allowedChars.test(username)) {
    errors.push('Username can only contain letters, numbers, and underscores');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export default config;
