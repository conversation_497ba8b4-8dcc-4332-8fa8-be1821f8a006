# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api/v1
NEXT_PUBLIC_WS_URL=http://localhost:3001

# App Configuration
NEXT_PUBLIC_APP_NAME=MikroTik Monitoring System
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_DESCRIPTION=Network monitoring system for MikroTik devices
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Session Configuration
NEXT_PUBLIC_SESSION_TIMEOUT=3600000

# UI Configuration
NEXT_PUBLIC_DASHBOARD_REFRESH_INTERVAL=30000
NEXT_PUBLIC_MAX_DATA_POINTS=100
NEXT_PUBLIC_NOTIFICATION_DURATION=5000
NEXT_PUBLIC_DEFAULT_PAGE_SIZE=20

# Feature Flags
NEXT_PUBLIC_ENABLE_DARK_MODE=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_EXPORT=true
NEXT_PUBLIC_ENABLE_ADVANCED_FILTERS=true
NEXT_PUBLIC_ENABLE_DEVICE_MAP=true

# Development Configuration
NEXT_PUBLIC_ENABLE_MOCK_DATA=true

# Timezone and Locale
NEXT_PUBLIC_TIMEZONE=UTC
NEXT_PUBLIC_LOCALE=en-US

# File Upload
NEXT_PUBLIC_MAX_FILE_SIZE=5242880

# WebSocket Configuration
NEXT_PUBLIC_WS_RECONNECT_ATTEMPTS=5
NEXT_PUBLIC_WS_RECONNECT_INTERVAL=3000

# API Timeout
NEXT_PUBLIC_API_TIMEOUT=10000
