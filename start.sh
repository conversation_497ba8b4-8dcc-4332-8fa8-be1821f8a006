#!/bin/bash

# MikroTik Monitoring System - Quick Start Script
# نص تشغيل سريع لنظام مراقبة المايكروتك

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Docker and Docker Compose
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Function to check Node.js and npm
check_nodejs() {
    print_status "Checking Node.js installation..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        echo "Visit: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Function to setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend .env
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_success "Created backend/.env from example"
        else
            print_warning "backend/.env.example not found, using default values"
        fi
    else
        print_status "backend/.env already exists"
    fi
    
    # Frontend .env.local
    if [ ! -f "frontend/.env.local" ]; then
        print_success "frontend/.env.local already created"
    else
        print_status "frontend/.env.local already exists"
    fi
}

# Function to start with Docker
start_with_docker() {
    print_status "Starting MikroTik Monitoring System with Docker..."
    
    # Check if docker-compose.yml exists
    if [ ! -f "docker-compose.yml" ]; then
        print_error "docker-compose.yml not found in current directory"
        exit 1
    fi
    
    # Stop any running containers
    print_status "Stopping any existing containers..."
    docker-compose down 2>/dev/null || true
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 10
    
    # Check service health
    print_status "Checking service health..."
    
    # Check backend health
    for i in {1..30}; do
        if curl -f http://localhost:3001/health >/dev/null 2>&1; then
            print_success "Backend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Backend health check failed"
            docker-compose logs backend
            exit 1
        fi
        sleep 2
    done
    
    # Check frontend
    for i in {1..30}; do
        if curl -f http://localhost:3000 >/dev/null 2>&1; then
            print_success "Frontend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Frontend health check failed"
            docker-compose logs frontend
            exit 1
        fi
        sleep 2
    done
    
    print_success "All services are running!"
    echo ""
    echo "🎉 MikroTik Monitoring System is ready!"
    echo ""
    echo "📱 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:3001"
    echo "📊 Health Check: http://localhost:3001/health"
    echo ""
    echo "🔑 Default Login:"
    echo "   Email: <EMAIL>"
    echo "   Password: admin123"
    echo ""
    echo "📝 View logs: docker-compose logs -f"
    echo "🛑 Stop system: docker-compose down"
}

# Function to start manually
start_manually() {
    print_status "Starting MikroTik Monitoring System manually..."
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in current directory"
        exit 1
    fi
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install all dependencies
    print_status "Installing all project dependencies..."
    npm run install:all
    
    # Setup backend
    print_status "Setting up backend database..."
    npm run setup:backend
    
    # Start services
    print_status "Starting development servers..."
    print_warning "This will start both backend and frontend in development mode"
    print_warning "Press Ctrl+C to stop all services"
    
    npm run dev
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -d, --docker     Start with Docker (recommended)"
    echo "  -m, --manual     Start manually (development)"
    echo "  -h, --help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --docker     # Start with Docker"
    echo "  $0 --manual     # Start manually"
    echo "  $0              # Interactive mode"
}

# Main function
main() {
    echo "🚀 MikroTik Monitoring System - Quick Start"
    echo "=========================================="
    echo ""
    
    # Parse command line arguments
    case "${1:-}" in
        -d|--docker)
            check_docker
            setup_env_files
            start_with_docker
            ;;
        -m|--manual)
            check_nodejs
            setup_env_files
            start_manually
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        "")
            # Interactive mode
            echo "Choose installation method:"
            echo "1) Docker (Recommended - Easy setup)"
            echo "2) Manual (Development - More control)"
            echo "3) Exit"
            echo ""
            read -p "Enter your choice (1-3): " choice
            
            case $choice in
                1)
                    check_docker
                    setup_env_files
                    start_with_docker
                    ;;
                2)
                    check_nodejs
                    setup_env_files
                    start_manually
                    ;;
                3)
                    echo "Goodbye!"
                    exit 0
                    ;;
                *)
                    print_error "Invalid choice. Please run the script again."
                    exit 1
                    ;;
            esac
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
