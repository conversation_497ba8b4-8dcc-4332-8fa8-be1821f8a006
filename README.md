# نظام مراقبة أجهزة المايكروتك
## MikroTik Network Monitoring System

نظام مراقبة شامل لأجهزة المايكروتك يوفر مراقبة في الوقت الفعلي، تنبيهات ذكية، ولوحة تحكم تفاعلية.

## المميزات الرئيسية

### 🔍 المراقبة الشاملة
- مراقبة حالة الأجهزة في الوقت الفعلي
- مراقبة استخدام المعالج والذاكرة
- مراقبة حركة البيانات على الواجهات
- مراقبة درجة الحرارة والفولتية
- مراقبة حالة الاتصال والـ Uptime

### 📊 لوحة التحكم التفاعلية
- عرض بصري لحالة جميع الأجهزة
- رسوم بيانية تفاعلية للأداء
- خرائط الشبكة التفاعلية
- تقارير مفصلة وإحصائيات

### 🚨 نظام التنبيهات الذكي
- تنبيهات فورية عند حدوث مشاكل
- تنبيهات عبر البريد الإلكتروني
- تنبيهات عبر الرسائل النصية
- تنبيهات مخصصة حسب نوع المشكلة

### 👥 إدارة المستخدمين
- نظام مصادقة آمن
- أدوار ومستويات وصول مختلفة
- سجل العمليات والأنشطة

## التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الويب
- **Socket.IO** - الاتصال في الوقت الفعلي
- **PostgreSQL** - قاعدة البيانات
- **Prisma** - ORM لقاعدة البيانات
- **RouterOS API** - للاتصال مع أجهزة المايكروتك

### Frontend
- **React** - مكتبة واجهة المستخدم
- **Next.js** - إطار عمل React
- **TypeScript** - لغة البرمجة المطورة
- **Tailwind CSS** - إطار عمل التصميم
- **Chart.js** - الرسوم البيانية
- **Socket.IO Client** - الاتصال في الوقت الفعلي

### DevOps & Monitoring
- **Docker** - الحاويات
- **Docker Compose** - إدارة الحاويات
- **Redis** - التخزين المؤقت
- **Nginx** - خادم الويب

## هيكل المشروع

```
mikrotik-monitoring/
├── backend/                 # خادم Node.js
│   ├── src/
│   │   ├── controllers/     # تحكم في العمليات
│   │   ├── models/          # نماذج البيانات
│   │   ├── routes/          # مسارات API
│   │   ├── services/        # خدمات العمل
│   │   ├── middleware/      # الوسطاء
│   │   ├── utils/           # الأدوات المساعدة
│   │   └── config/          # إعدادات النظام
│   ├── prisma/              # مخططات قاعدة البيانات
│   └── package.json
├── frontend/                # تطبيق React
│   ├── src/
│   │   ├── components/      # مكونات واجهة المستخدم
│   │   ├── pages/           # صفحات التطبيق
│   │   ├── hooks/           # React Hooks مخصصة
│   │   ├── services/        # خدمات API
│   │   ├── utils/           # الأدوات المساعدة
│   │   └── types/           # تعريفات TypeScript
│   └── package.json
├── docker-compose.yml       # إعدادات Docker
├── nginx.conf              # إعدادات Nginx
└── README.md
```

## البيانات المراقبة

### معلومات الجهاز الأساسية
- اسم الجهاز ونوعه
- عنوان IP وMAC Address
- إصدار RouterOS
- وقت التشغيل (Uptime)
- حالة الاتصال

### مقاييس الأداء
- استخدام المعالج (CPU Usage)
- استخدام الذاكرة (Memory Usage)
- استخدام التخزين (Storage Usage)
- درجة الحرارة
- الفولتية

### مراقبة الشبكة
- حركة البيانات على الواجهات
- عدد الاتصالات النشطة
- معدل الأخطاء
- جودة الإشارة (للأجهزة اللاسلكية)

### الأمان
- محاولات تسجيل الدخول
- القواعد المحظورة
- حركة البيانات المشبوهة

## 🚀 التشغيل السريع

### الطريقة الأولى: تشغيل تلقائي (Windows)
```bash
# تشغيل الملف التلقائي
start.bat
```

### الطريقة الثانية: Docker (موصى به)
```bash
# تشغيل النظام بالكامل
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

### الطريقة الثالثة: تطوير يدوي
```bash
# تشغيل سريع للتطوير
dev-start.bat

# أو يدوياً
npm run install:all
npm run setup:backend
npm run dev
```

### الوصول للنظام
- **الواجهة الأمامية**: http://localhost:3000
- **API الخلفية**: http://localhost:3001
- **بيانات الدخول الافتراضية**:
  - Email: <EMAIL>
  - Password: admin123

## الإعداد

### إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb mikrotik_monitoring

# تشغيل الهجرات
npm run prisma:migrate
```

### إعداد متغيرات البيئة
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الإعدادات
nano .env
```

## الاستخدام

1. افتح المتصفح وانتقل إلى `http://localhost:3000`
2. سجل دخولك باستخدام الحساب الافتراضي
3. أضف أجهزة المايكروتك من صفحة الإعدادات
4. ابدأ مراقبة أجهزتك!

## المساهمة

نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم، يرجى فتح issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.
