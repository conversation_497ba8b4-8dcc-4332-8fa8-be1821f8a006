{"name": "mikrotik-monitoring-backend", "version": "1.0.0", "description": "Backend API for MikroTik Network Monitoring System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["mikrotik", "network", "monitoring", "routeros", "api", "nodejs", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "redis": "^4.6.11", "mikronode": "^2.3.11", "snmp-native": "^1.0.25", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}