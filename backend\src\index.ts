import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';

// Import configurations and utilities
import { config } from '@/config/config';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
// import { authenticateToken } from '@/middleware/auth';

// Import routes
import authRoutes from '@/routes/auth';
import deviceRoutes from '@/routes/devices';
import alertRoutes from '@/routes/alerts';
import userRoutes from '@/routes/users';
import dashboardRoutes from '@/routes/dashboard';
import settingsRoutes from '@/routes/settings';

// Import services
import { DatabaseService } from '@/services/database';
import { MonitoringService } from '@/services/monitoring';
import { AlertService } from '@/services/alert';
import { SocketService } from '@/services/socket';

// Load environment variables
dotenv.config();

class Application {
  private app: express.Application;
  private server: any;
  private io: Server;
  private port: number;

  constructor() {
    this.app = express();
    this.port = config.port;
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: config.cors.origin,
        credentials: config.cors.credentials,
      },
    });

    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeServices();
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        error: 'Too many requests from this IP, please try again later.',
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Body parsing and compression
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging
    if (config.nodeEnv !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => logger.info(message.trim()),
        },
      }));
    }

    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.nodeEnv,
        version: process.env['npm_package_version'] || '1.0.0',
      });
    });
  }

  private initializeRoutes(): void {
    const apiRouter = express.Router();

    // Public routes
    apiRouter.use('/auth', authRoutes);

    // Protected routes
    apiRouter.use('/devices', deviceRoutes);
    apiRouter.use('/alerts', alertRoutes);
    apiRouter.use('/users', userRoutes);
    apiRouter.use('/dashboard', dashboardRoutes);
    apiRouter.use('/settings', settingsRoutes);

    // Mount API routes
    this.app.use(`/api/${config.apiVersion}`, apiRouter);

    // API documentation
    this.app.get('/api', (_req, res) => {
      res.json({
        name: 'MikroTik Monitoring API',
        version: config.apiVersion,
        description: 'REST API for MikroTik Network Monitoring System',
        endpoints: {
          auth: '/api/v1/auth',
          devices: '/api/v1/devices',
          alerts: '/api/v1/alerts',
          users: '/api/v1/users',
          dashboard: '/api/v1/dashboard',
          settings: '/api/v1/settings',
        },
        documentation: '/api/docs',
        health: '/health',
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  private async initializeServices(): Promise<void> {
    try {
      // Initialize database
      await DatabaseService.initialize();
      logger.info('Database service initialized');

      // Initialize Socket.IO service
      SocketService.initialize(this.io);
      logger.info('Socket.IO service initialized');

      // Initialize monitoring service
      await MonitoringService.initialize();
      logger.info('Monitoring service initialized');

      // Initialize alert service
      await AlertService.initialize();
      logger.info('Alert service initialized');

    } catch (error) {
      logger.error('Failed to initialize services:', error);
      process.exit(1);
    }
  }

  public async start(): Promise<void> {
    try {
      this.server.listen(this.port, () => {
        logger.info(`🚀 Server running on port ${this.port}`);
        logger.info(`📊 Environment: ${config.nodeEnv}`);
        logger.info(`🔗 API Base URL: http://localhost:${this.port}/api/${config.apiVersion}`);
        logger.info(`💻 Health Check: http://localhost:${this.port}/health`);
      });

      // Graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Stop accepting new connections
      this.server.close(async () => {
        logger.info('HTTP server closed');

        try {
          // Close database connections
          await DatabaseService.disconnect();
          logger.info('Database connections closed');

          // Stop monitoring services
          await MonitoringService.stop();
          logger.info('Monitoring service stopped');

          // Stop alert service
          await AlertService.stop();
          logger.info('Alert service stopped');

          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during graceful shutdown:', error);
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
  }
}

// Start the application
const app = new Application();
app.start().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});

export default app;
