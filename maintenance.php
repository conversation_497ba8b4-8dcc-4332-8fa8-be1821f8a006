<?php
// صفحة الصيانة
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموقع تحت الصيانة - الرصين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .maintenance-icon {
            font-size: 5rem;
            margin-bottom: 1.5rem;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .maintenance-title {
            color: #343a40;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .maintenance-message {
            color: #6c757d;
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .progress-container {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            margin: 2rem 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            border-radius: 10px;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 0%; }
        }
        
        .contact-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .contact-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: #6c757d;
        }
        
        .social-links {
            margin-top: 2rem;
        }
        
        .social-link {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            line-height: 50px;
            margin: 0 10px;
            transition: transform 0.3s ease;
        }
        
        .social-link:hover {
            transform: translateY(-3px) scale(1.1);
            color: white;
        }
        
        .estimated-time {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1.5rem 0;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-message {
                font-size: 1.1rem;
            }
            
            .maintenance-icon {
                font-size: 4rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">🔧</div>
        <h1 class="maintenance-title">الموقع تحت الصيانة</h1>
        <p class="maintenance-message">
            نعتذر عن الإزعاج، نحن نعمل حالياً على تحسين وتطوير النظام لتقديم خدمة أفضل لكم.
            <br>
            سيعود الموقع للعمل قريباً بإذن الله.
        </p>
        
        <div class="progress-container">
            <div class="progress-bar"></div>
        </div>
        
        <div class="estimated-time">
            ⏰ الوقت المتوقع للانتهاء: خلال ساعتين
        </div>
        
        <div class="contact-info">
            <h4 class="contact-title">للاستفسارات والدعم الفني</h4>
            <div class="contact-item">
                <span>📧</span>
                <span><EMAIL></span>
            </div>
            <div class="contact-item">
                <span>📱</span>
                <span>+964 XXX XXX XXXX</span>
            </div>
            <div class="contact-item">
                <span>🕐</span>
                <span>من الساعة 8:00 صباحاً إلى 4:00 مساءً</span>
            </div>
        </div>
        
        <div class="social-links">
            <a href="#" class="social-link" title="فيسبوك">📘</a>
            <a href="#" class="social-link" title="تويتر">🐦</a>
            <a href="#" class="social-link" title="واتساب">💬</a>
            <a href="#" class="social-link" title="تلغرام">✈️</a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                © <?php echo date('Y'); ?> الرصين - جميع الحقوق محفوظة
            </small>
        </div>
    </div>
    
    <script>
        // تحديث الوقت كل دقيقة
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-IQ');
            console.log('الوقت الحالي:', timeString);
        }, 60000);
        
        // إضافة تأثير تفاعلي للأيقونات الاجتماعية
        document.querySelectorAll('.social-link').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.1)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // محاولة إعادة تحميل الصفحة كل 5 دقائق للتحقق من انتهاء الصيانة
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 دقائق
    </script>
</body>
</html>
