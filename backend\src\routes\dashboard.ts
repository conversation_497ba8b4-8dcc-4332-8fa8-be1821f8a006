import { Router } from 'express';
import { authenticateToken } from '@/middleware/auth';
import { prisma } from '@/services/database';
import { logger } from '@/utils/logger';

const router = Router();

// Get dashboard overview
router.get('/overview', authenticateToken, async (_req, res) => {
  try {
    // Get device statistics
    const deviceStats = await prisma.device.groupBy({
      by: ['status'],
      _count: true
    });

    // Get alert statistics
    const alertStats = await prisma.alert.groupBy({
      by: ['status', 'severity'],
      _count: true
    });

    // Get recent alerts
    const recentAlerts = await prisma.alert.findMany({
      where: { status: 'ACTIVE' },
      include: {
        device: {
          select: { id: true, name: true, ipAddress: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    // Process device statistics
    const devices = {
      total: 0,
      online: 0,
      offline: 0,
      warning: 0
    };

    deviceStats.forEach(stat => {
      devices.total += stat._count;
      if (stat.status === 'ONLINE') devices.online += stat._count;
      else if (stat.status === 'OFFLINE') devices.offline += stat._count;
      else if (stat.status === 'WARNING') devices.warning += stat._count;
    });

    // Process alert statistics
    const alerts = {
      total: 0,
      active: 0,
      acknowledged: 0,
      resolved: 0,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    };

    alertStats.forEach(stat => {
      alerts.total += stat._count;
      
      if (stat.status === 'ACTIVE') alerts.active += stat._count;
      else if (stat.status === 'ACKNOWLEDGED') alerts.acknowledged += stat._count;
      else if (stat.status === 'RESOLVED') alerts.resolved += stat._count;

      if (stat.severity === 'CRITICAL') alerts.critical += stat._count;
      else if (stat.severity === 'HIGH') alerts.high += stat._count;
      else if (stat.severity === 'MEDIUM') alerts.medium += stat._count;
      else if (stat.severity === 'LOW') alerts.low += stat._count;
    });

    res.json({
      devices,
      alerts,
      recentAlerts
    });
  } catch (error) {
    logger.error('Error fetching dashboard overview:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard overview' });
  }
});

// Get device metrics for charts
router.get('/metrics', authenticateToken, async (req, res) => {
  try {
    const { deviceId, timeRange = '24h' } = req.query;

    let startTime = new Date();
    switch (timeRange) {
      case '1h':
        startTime.setHours(startTime.getHours() - 1);
        break;
      case '6h':
        startTime.setHours(startTime.getHours() - 6);
        break;
      case '24h':
        startTime.setHours(startTime.getHours() - 24);
        break;
      case '7d':
        startTime.setDate(startTime.getDate() - 7);
        break;
      case '30d':
        startTime.setDate(startTime.getDate() - 30);
        break;
      default:
        startTime.setHours(startTime.getHours() - 24);
    }

    const where: any = {
      timestamp: {
        gte: startTime
      }
    };

    if (deviceId) {
      where.deviceId = deviceId;
    }

    const metrics = await prisma.deviceMetric.findMany({
      where,
      include: {
        device: {
          select: { id: true, name: true }
        }
      },
      orderBy: { timestamp: 'asc' }
    });

    res.json(metrics);
  } catch (error) {
    logger.error('Error fetching dashboard metrics:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard metrics' });
  }
});

// Get network topology data
router.get('/topology', authenticateToken, async (_req, res) => {
  try {
    const devices = await prisma.device.findMany({
      include: {
        interfaces: true,
        alerts: {
          where: { status: 'ACTIVE' }
        }
      }
    });

    // Transform data for network topology visualization
    const nodes = devices.map(device => ({
      id: device.id,
      name: device.name,
      ip: device.ipAddress,
      status: device.status,
      type: device.deviceType || 'ROUTER',
      alertCount: device.alerts.length,
      interfaceCount: device.interfaces.length
    }));

    // For now, we'll create a simple topology
    // In a real implementation, you'd analyze routing tables and connections
    const links = [];

    res.json({
      nodes,
      links
    });
  } catch (error) {
    logger.error('Error fetching network topology:', error);
    res.status(500).json({ error: 'Failed to fetch network topology' });
  }
});

// Get system health summary
router.get('/health', authenticateToken, async (_req, res) => {
  try {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Get latest metrics for each device
    const latestMetrics = await prisma.deviceMetric.findMany({
      where: {
        timestamp: {
          gte: oneHourAgo
        }
      },
      include: {
        device: {
          select: { id: true, name: true, status: true }
        }
      },
      orderBy: { timestamp: 'desc' }
    });

    // Group by device and get the latest metric for each
    const deviceMetrics = new Map();
    latestMetrics.forEach(metric => {
      if (!deviceMetrics.has(metric.deviceId)) {
        deviceMetrics.set(metric.deviceId, metric);
      }
    });

    const healthSummary = Array.from(deviceMetrics.values()).map(metric => ({
      deviceId: metric.device.id,
      deviceName: metric.device.name,
      status: metric.device.status,
      cpuUsage: metric.cpuUsage,
      memoryUsage: metric.memoryUsage,
      temperature: metric.temperature,
      voltage: metric.voltage,
      timestamp: metric.timestamp
    }));

    res.json(healthSummary);
  } catch (error) {
    logger.error('Error fetching system health:', error);
    res.status(500).json({ error: 'Failed to fetch system health' });
  }
});

export default router;
