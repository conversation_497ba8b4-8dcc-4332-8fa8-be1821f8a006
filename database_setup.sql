-- إعد<PERSON> قاعدة البيانات للمشروع الرصين
-- تاريخ الإنشاء: 2025-06-25

-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `rasseem` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `rasseem`;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `ar_name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('الإدارة العامة','مدير','إدارة الجوال','المخابر','الهندسة','الصيانة') NOT NULL,
  `active` tinyint(1) DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(45) NOT NULL,
  `attempts` int(11) DEFAULT 1,
  `last_attempt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_ip` (`ip`),
  KEY `idx_ip` (`ip`),
  KEY `idx_last_attempt` (`last_attempt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجلات تسجيل الدخول
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `ip` varchar(45) NOT NULL,
  `user_agent` text,
  `login_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `logout_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_login_logs_user` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  CONSTRAINT `fk_login_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإشعارات
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_notifications_user` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجلات الأنشطة
CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `details` text,
  `ip` varchar(45) NOT NULL,
  `user_agent` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_activity_logs_user` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_activity_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التذاكر/الطلبات
CREATE TABLE IF NOT EXISTS `tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_number` varchar(20) NOT NULL UNIQUE,
  `user_id` int(11) NOT NULL,
  `assigned_to` int(11) DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `priority` enum('منخفض','متوسط','عالي','عاجل') DEFAULT 'متوسط',
  `status` enum('جديد','قيد المعالجة','في الانتظار','مكتمل','مغلق','ملغي') DEFAULT 'جديد',
  `category` varchar(50) DEFAULT NULL,
  `attachments` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `closed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_ticket_number` (`ticket_number`),
  KEY `fk_tickets_user` (`user_id`),
  KEY `fk_tickets_assigned` (`assigned_to`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_tickets_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tickets_assigned` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تعليقات التذاكر
CREATE TABLE IF NOT EXISTS `ticket_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `is_internal` tinyint(1) DEFAULT 0,
  `attachments` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_ticket_comments_ticket` (`ticket_id`),
  KEY `fk_ticket_comments_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_ticket_comments_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ticket_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإعدادات
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text,
  `description` varchar(255),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting_key` (`setting_key`),
  KEY `fk_settings_user` (`updated_by`),
  CONSTRAINT `fk_settings_user` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المستخدم الافتراضي (admin)
INSERT INTO `users` (`username`, `password`, `ar_name`, `email`, `role`, `active`) VALUES
('admin', '$argon2id$v=19$m=65536,t=4,p=3$YWRtaW5wYXNzd29yZA$8K9vF2zF5K8vF2zF5K8vF2zF5K8vF2zF5K8vF2zF5K8', 'المدير العام', '<EMAIL>', 'الإدارة العامة', 1);

-- إدراج الإعدادات الافتراضية
INSERT INTO `settings` (`setting_key`, `setting_value`, `description`) VALUES
('site_name', 'الرصين', 'اسم الموقع'),
('site_description', 'منصة تنظيم التواصل بين وحدات قسم الإتصالات', 'وصف الموقع'),
('max_file_size', '5242880', 'الحد الأقصى لحجم الملف بالبايت (5 ميجابايت)'),
('allowed_file_types', 'jpg,jpeg,png,pdf,doc,docx', 'أنواع الملفات المسموحة'),
('session_timeout', '14400', 'مدة انتهاء الجلسة بالثواني (4 ساعات)'),
('backup_enabled', '1', 'تفعيل النسخ الاحتياطي التلقائي'),
('maintenance_mode', '0', 'وضع الصيانة');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_users_role_active ON users(role, active);
CREATE INDEX idx_tickets_status_priority ON tickets(status, priority);
CREATE INDEX idx_tickets_user_status ON tickets(user_id, status);
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);

-- إنشاء مشغلات (Triggers) لتسجيل الأنشطة
DELIMITER $$

CREATE TRIGGER tr_users_insert AFTER INSERT ON users
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (user_id, action, details, ip, created_at)
    VALUES (NEW.id, 'user_created', CONCAT('تم إنشاء مستخدم جديد: ', NEW.ar_name), '127.0.0.1', NOW());
END$$

CREATE TRIGGER tr_tickets_insert AFTER INSERT ON tickets
FOR EACH ROW
BEGIN
    INSERT INTO activity_logs (user_id, action, details, ip, created_at)
    VALUES (NEW.user_id, 'ticket_created', CONCAT('تم إنشاء تذكرة جديدة: ', NEW.ticket_number), '127.0.0.1', NOW());
END$$

CREATE TRIGGER tr_tickets_update AFTER UPDATE ON tickets
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO activity_logs (user_id, action, details, ip, created_at)
        VALUES (NEW.user_id, 'ticket_status_changed', CONCAT('تم تغيير حالة التذكرة ', NEW.ticket_number, ' من ', OLD.status, ' إلى ', NEW.status), '127.0.0.1', NOW());
    END IF;
END$$

DELIMITER ;

-- إنشاء إجراءات مخزنة (Stored Procedures)
DELIMITER $$

CREATE PROCEDURE GetUserStatistics(IN user_id INT)
BEGIN
    SELECT 
        COUNT(CASE WHEN status = 'جديد' THEN 1 END) as new_tickets,
        COUNT(CASE WHEN status = 'قيد المعالجة' THEN 1 END) as in_progress_tickets,
        COUNT(CASE WHEN status = 'مكتمل' THEN 1 END) as completed_tickets,
        COUNT(*) as total_tickets
    FROM tickets 
    WHERE user_id = user_id;
END$$

CREATE PROCEDURE CleanupOldData()
BEGIN
    -- حذف محاولات تسجيل الدخول القديمة
    DELETE FROM login_attempts WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- حذف سجلات تسجيل الدخول القديمة
    DELETE FROM login_logs WHERE login_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- حذف الإشعارات المقروءة القديمة
    DELETE FROM notifications WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- حذف سجلات الأنشطة القديمة
    DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 180 DAY);
END$$

DELIMITER ;

-- إنشاء مستخدم قاعدة البيانات المخصص للتطبيق
-- CREATE USER 'rasseem_user'@'localhost' IDENTIFIED BY 'secure_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON rasseem.* TO 'rasseem_user'@'localhost';
-- FLUSH PRIVILEGES;

-- تحسين إعدادات قاعدة البيانات
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL query_cache_size = 32M;
SET GLOBAL query_cache_type = ON;
