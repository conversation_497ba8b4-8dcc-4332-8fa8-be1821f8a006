import { Router } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import Joi from 'joi';
import { prisma } from '@/services/database';
import { logger, loggerHelpers } from '@/utils/logger';
import { config } from '@/config/config';
import { asyncHandler, validate, ValidationError, UnauthorizedError, ConflictError } from '@/middleware/errorHandler';
import { authenticateToken, AuthenticatedRequest } from '@/middleware/auth';

const router = Router();

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  username: Joi.string().alphanum().min(3).max(30).required(),
  password: Joi.string().min(6).required(),
  firstName: Joi.string().min(1).max(50).optional(),
  lastName: Joi.string().min(1).max(50).optional(),
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(6).required(),
});

const forgotPasswordSchema = Joi.object({
  email: Joi.string().email().required(),
});

const resetPasswordSchema = Joi.object({
  token: Joi.string().required(),
  password: Joi.string().min(6).required(),
});

/**
 * Generate JWT tokens
 */
const generateTokens = (userId: string) => {
  const payload = { userId };
  const options1 = { expiresIn: config.jwt.expiresIn as string };
  const options2 = { expiresIn: config.jwt.refreshExpiresIn as string };

  const accessToken = jwt.sign(
    payload,
    config.jwt.secret as string,
    options1
  );

  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    config.jwt.secret as string,
    options2
  );

  return { accessToken, refreshToken };
};

/**
 * POST /auth/login
 * User login
 */
router.post('/login', validate(loginSchema), asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Find user by email
  const user = await prisma.user.findUnique({
    where: { email },
  });

  if (!user) {
    loggerHelpers.logAuth('Login failed - user not found', undefined, req.ip, false);
    throw new UnauthorizedError('Invalid email or password');
  }

  if (!user.isActive) {
    loggerHelpers.logAuth('Login failed - user inactive', user.id, req.ip, false);
    throw new UnauthorizedError('Account is inactive. Please contact administrator.');
  }

  // Verify password
  const isValidPassword = await bcrypt.compare(password, user.password);
  if (!isValidPassword) {
    loggerHelpers.logAuth('Login failed - invalid password', user.id, req.ip, false);
    throw new UnauthorizedError('Invalid email or password');
  }

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id);

  // Log successful login
  loggerHelpers.logAuth('Login successful', user.id, req.ip, true);

  res.json({
    message: 'Login successful',
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
    },
    tokens: {
      accessToken,
      refreshToken,
    },
  });
}));

/**
 * POST /auth/register
 * User registration
 */
router.post('/register', validate(registerSchema), asyncHandler(async (req, res) => {
  const { email, username, password, firstName, lastName } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { email },
        { username },
      ],
    },
  });

  if (existingUser) {
    const field = existingUser.email === email ? 'email' : 'username';
    throw new ConflictError(`User with this ${field} already exists`);
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

  // Create user
  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      firstName,
      lastName,
      role: 'USER', // Default role
      isActive: true,
    },
  });

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id);

  // Log successful registration
  loggerHelpers.logAuth('Registration successful', user.id, req.ip, true);

  res.status(201).json({
    message: 'Registration successful',
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
    },
    tokens: {
      accessToken,
      refreshToken,
    },
  });
}));

/**
 * POST /auth/refresh
 * Refresh access token
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new ValidationError('Refresh token is required');
  }

  try {
    const decoded = jwt.verify(refreshToken, config.jwt.secret as string) as any;

    if (decoded.type !== 'refresh') {
      throw new UnauthorizedError('Invalid refresh token');
    }

    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
    });

    if (!user || !user.isActive) {
      throw new UnauthorizedError('User not found or inactive');
    }

    // Generate new tokens
    const tokens = generateTokens(user.id);

    loggerHelpers.logAuth('Token refreshed', user.id, req.ip, true);

    res.json({
      message: 'Token refreshed successfully',
      tokens,
    });

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new UnauthorizedError('Invalid refresh token');
    }
    throw error;
  }
}));

/**
 * POST /auth/logout
 * User logout (client-side token removal)
 */
router.post('/logout', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  loggerHelpers.logAuth('Logout', req.user!.id, req.ip, true);

  res.json({
    message: 'Logout successful',
  });
}));

/**
 * GET /auth/me
 * Get current user profile
 */
router.get('/me', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      username: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!user) {
    throw new UnauthorizedError('User not found');
  }

  res.json({
    user,
  });
}));

/**
 * PUT /auth/profile
 * Update user profile
 */
router.put('/profile', authenticateToken, validate(Joi.object({
  firstName: Joi.string().min(1).max(50).optional(),
  lastName: Joi.string().min(1).max(50).optional(),
})), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { firstName, lastName } = req.body;

  const user = await prisma.user.update({
    where: { id: req.user!.id },
    data: {
      firstName,
      lastName,
    },
    select: {
      id: true,
      username: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
    },
  });

  loggerHelpers.logAuth('Profile updated', user.id, req.ip, true);

  res.json({
    message: 'Profile updated successfully',
    user,
  });
}));

/**
 * PUT /auth/change-password
 * Change user password
 */
router.put('/change-password', authenticateToken, validate(changePasswordSchema), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { currentPassword, newPassword } = req.body;

  // Get current user with password
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
  });

  if (!user) {
    throw new UnauthorizedError('User not found');
  }

  // Verify current password
  const isValidPassword = await bcrypt.compare(currentPassword, user.password);
  if (!isValidPassword) {
    loggerHelpers.logAuth('Password change failed - invalid current password', user.id, req.ip, false);
    throw new UnauthorizedError('Current password is incorrect');
  }

  // Hash new password
  const hashedPassword = await bcrypt.hash(newPassword, config.security.bcryptRounds);

  // Update password
  await prisma.user.update({
    where: { id: user.id },
    data: { password: hashedPassword },
  });

  loggerHelpers.logAuth('Password changed', user.id, req.ip, true);

  res.json({
    message: 'Password changed successfully',
  });
}));

/**
 * POST /auth/forgot-password
 * Request password reset
 */
router.post('/forgot-password', validate(forgotPasswordSchema), asyncHandler(async (req, res) => {
  const { email } = req.body;

  const user = await prisma.user.findUnique({
    where: { email },
  });

  // Always return success to prevent email enumeration
  if (!user) {
    res.json({
      message: 'If an account with that email exists, a password reset link has been sent.',
    });
    return;
  }

  // Generate reset token (in a real app, you'd store this and send via email)
  const resetToken = jwt.sign(
    { userId: user.id, type: 'reset' },
    config.jwt.secret as string,
    { expiresIn: '1h' }
  );

  // TODO: Send email with reset link
  logger.info(`Password reset requested for user ${user.id}. Reset token: ${resetToken}`);

  res.json({
    message: 'If an account with that email exists, a password reset link has been sent.',
    // In development, include the token
    ...(config.nodeEnv === 'development' && { resetToken }),
  });
}));

/**
 * POST /auth/reset-password
 * Reset password with token
 */
router.post('/reset-password', validate(resetPasswordSchema), asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  try {
    const decoded = jwt.verify(token, config.jwt.secret as string) as any;

    if (decoded.type !== 'reset') {
      throw new UnauthorizedError('Invalid reset token');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

    // Update password
    await prisma.user.update({
      where: { id: decoded.userId },
      data: { password: hashedPassword },
    });

    loggerHelpers.logAuth('Password reset', decoded.userId, req.ip, true);

    res.json({
      message: 'Password reset successfully',
    });

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new UnauthorizedError('Invalid or expired reset token');
    }
    throw error;
  }
}));

export default router;
