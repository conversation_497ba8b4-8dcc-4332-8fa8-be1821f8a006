const MikroNode = require('mikronode');
import { logger } from '@/utils/logger';

export interface MikroTikDevice {
  id: string;
  name: string;
  ipAddress: string;
  apiPort: number;
  username: string;
  password: string;
}

export interface DeviceInfo {
  identity: string;
  version: string;
  architecture: string;
  boardName: string;
  platform: string;
  uptime: string;
  buildTime: string;
  factoryFirmware: string;
  freeMemory: number;
  totalMemory: number;
  freeDiskSpace: number;
  totalDiskSpace: number;
  writeSectSinceReboot: number;
  writeSectTotal: number;
  badBlocks: number;
}

export interface SystemResource {
  uptime: string;
  version: string;
  buildTime: string;
  freeMemory: number;
  totalMemory: number;
  cpu: string;
  cpuCount: number;
  cpuFrequency: number;
  cpuLoad: number;
  freeDiskSpace: number;
  totalDiskSpace: number;
  architecture: string;
  boardName: string;
  platform: string;
}

export interface InterfaceInfo {
  name: string;
  type: string;
  actualMtu: number;
  l2Mtu: number;
  macAddress: string;
  lastLinkDownTime: string;
  lastLinkUpTime: string;
  linkDowns: number;
  running: boolean;
  disabled: boolean;
  comment: string;
  rxByte: number;
  txByte: number;
  rxPacket: number;
  txPacket: number;
  rxDrop: number;
  txDrop: number;
  rxError: number;
  txError: number;
}

export interface SystemHealth {
  voltage: number;
  temperature: number;
  cpuTemperature?: number;
  fanSpeed?: number;
  powerConsumption?: number;
}

class MikroTikService {
  private connections: Map<string, any> = new Map();

  /**
   * Execute a command on a channel and return the result
   */
  private async executeChannelCommand(connection: any, command: string, args?: string[]): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const channel = connection.openChannel();
      const results: any[] = [];

      channel.on('data', (data: any) => {
        if (data && data.data) {
          results.push(MikroNode.resultsToObj(data.data));
        }
      });

      channel.on('done', (data: any) => {
        if (data && data.data) {
          data.data.forEach((item: any) => {
            results.push(MikroNode.resultsToObj(item.data));
          });
        }
        channel.close();
        resolve(results);
      });

      channel.on('trap', (error: any) => {
        channel.close();
        reject(new Error(error.message || 'Command failed'));
      });

      // Set timeout
      setTimeout(() => {
        channel.close();
        reject(new Error('Command timeout'));
      }, 10000);

      // Execute command
      if (args && args.length > 0) {
        channel.write([command, ...args]);
      } else {
        channel.write(command);
      }
    });
  }

  /**
   * Connect to a MikroTik device
   */
  public async connect(device: MikroTikDevice): Promise<any> {
    try {
      const connectionKey = `${device.ipAddress}:${device.apiPort}`;

      // Check if connection already exists
      if (this.connections.has(connectionKey)) {
        const existingConnection = this.connections.get(connectionKey)!;
        if (existingConnection.connected && existingConnection.connected()) {
          return existingConnection;
        } else {
          // Remove stale connection
          this.connections.delete(connectionKey);
        }
      }

      const mikroDevice = new MikroNode(device.ipAddress, device.apiPort);

      const connection = await new Promise((resolve, reject) => {
        mikroDevice.connect()
          .then(([login]: any) => login(device.username, device.password))
          .then((conn: any) => {
            resolve(conn);
          })
          .catch(reject);
      });

      this.connections.set(connectionKey, connection);

      logger.info(`Connected to MikroTik device: ${device.name} (${device.ipAddress})`);
      return connection;

    } catch (error) {
      logger.error(`Failed to connect to MikroTik device ${device.name} (${device.ipAddress}):`, error);
      throw error;
    }
  }

  /**
   * Disconnect from a MikroTik device
   */
  public async disconnect(device: MikroTikDevice): Promise<void> {
    try {
      const connectionKey = `${device.ipAddress}:${device.apiPort}`;
      const connection = this.connections.get(connectionKey);

      if (connection && connection.connected && connection.connected()) {
        connection.close();
        this.connections.delete(connectionKey);
        logger.info(`Disconnected from MikroTik device: ${device.name} (${device.ipAddress})`);
      }
    } catch (error) {
      logger.error(`Error disconnecting from MikroTik device ${device.name}:`, error);
    }
  }

  /**
   * Test connection to a MikroTik device
   */
  public async testConnection(device: MikroTikDevice): Promise<boolean> {
    try {
      const connection = await this.connect(device);
      const channel = connection.openChannel();

      await new Promise((resolve, reject) => {
        channel.write('/system/identity/print');
        channel.on('done', resolve);
        channel.on('trap', reject);
        setTimeout(() => reject(new Error('Timeout')), 5000);
      });

      channel.close();
      await this.disconnect(device);
      return true;
    } catch (error) {
      logger.error(`Connection test failed for device ${device.name}:`, error);
      return false;
    }
  }

  /**
   * Get device information
   */
  public async getDeviceInfo(device: MikroTikDevice): Promise<DeviceInfo> {
    try {
      const connection = await this.connect(device);

      const identityData = await this.executeChannelCommand(connection, '/system/identity/print');
      const resourceData = await this.executeChannelCommand(connection, '/system/resource/print');
      const routerboardData = await this.executeChannelCommand(connection, '/system/routerboard/print').catch(() => []);

      const identity = identityData[0] || {};
      const resource = resourceData[0] || {};
      const routerboard = routerboardData[0] || {};

      return {
        identity: identity.name || 'Unknown',
        version: resource.version || 'Unknown',
        architecture: resource.architecture || 'Unknown',
        boardName: resource['board-name'] || 'Unknown',
        platform: resource.platform || 'Unknown',
        uptime: resource.uptime || '0s',
        buildTime: resource['build-time'] || 'Unknown',
        factoryFirmware: routerboard['factory-firmware'] || 'Unknown',
        freeMemory: parseInt(resource['free-memory']) || 0,
        totalMemory: parseInt(resource['total-memory']) || 0,
        freeDiskSpace: parseInt(resource['free-hdd-space']) || 0,
        totalDiskSpace: parseInt(resource['total-hdd-space']) || 0,
        writeSectSinceReboot: parseInt(resource['write-sect-since-reboot']) || 0,
        writeSectTotal: parseInt(resource['write-sect-total']) || 0,
        badBlocks: parseInt(resource['bad-blocks']) || 0,
      };

    } catch (error) {
      logger.error(`Error getting device info for ${device.name}:`, error);
      throw error;
    }
  }

  /**
   * Get system resources
   */
  public async getSystemResource(device: MikroTikDevice): Promise<SystemResource> {
    try {
      const connection = await this.connect(device);
      const resourceData = await this.executeChannelCommand(connection, '/system/resource/print');
      const resource = resourceData[0] || {};

      return {
        uptime: resource.uptime || '0s',
        version: resource.version || 'Unknown',
        buildTime: resource['build-time'] || 'Unknown',
        freeMemory: parseInt(resource['free-memory']) || 0,
        totalMemory: parseInt(resource['total-memory']) || 0,
        cpu: resource.cpu || 'Unknown',
        cpuCount: parseInt(resource['cpu-count']) || 1,
        cpuFrequency: parseInt(resource['cpu-frequency']) || 0,
        cpuLoad: parseInt(resource['cpu-load']) || 0,
        freeDiskSpace: parseInt(resource['free-hdd-space']) || 0,
        totalDiskSpace: parseInt(resource['total-hdd-space']) || 0,
        architecture: resource.architecture || 'Unknown',
        boardName: resource['board-name'] || 'Unknown',
        platform: resource.platform || 'Unknown',
      };

    } catch (error) {
      logger.error(`Error getting system resource for ${device.name}:`, error);
      throw error;
    }
  }

  /**
   * Get interface information
   */
  public async getInterfaces(device: MikroTikDevice): Promise<InterfaceInfo[]> {
    try {
      const connection = await this.connect(device);
      const interfaces = await this.executeChannelCommand(connection, '/interface/print', ['=stats']);

      return interfaces.map((iface: any) => ({
        name: iface.name || 'Unknown',
        type: iface.type || 'Unknown',
        actualMtu: parseInt(iface['actual-mtu']) || 0,
        l2Mtu: parseInt(iface['l2mtu']) || 0,
        macAddress: iface['mac-address'] || '',
        lastLinkDownTime: iface['last-link-down-time'] || '',
        lastLinkUpTime: iface['last-link-up-time'] || '',
        linkDowns: parseInt(iface['link-downs']) || 0,
        running: iface.running === 'true',
        disabled: iface.disabled === 'true',
        comment: iface.comment || '',
        rxByte: parseInt(iface['rx-byte']) || 0,
        txByte: parseInt(iface['tx-byte']) || 0,
        rxPacket: parseInt(iface['rx-packet']) || 0,
        txPacket: parseInt(iface['tx-packet']) || 0,
        rxDrop: parseInt(iface['rx-drop']) || 0,
        txDrop: parseInt(iface['tx-drop']) || 0,
        rxError: parseInt(iface['rx-error']) || 0,
        txError: parseInt(iface['tx-error']) || 0,
      }));

    } catch (error) {
      logger.error(`Error getting interfaces for ${device.name}:`, error);
      throw error;
    }
  }

  /**
   * Get system health information
   */
  public async getSystemHealth(device: MikroTikDevice): Promise<SystemHealth | null> {
    try {
      const connection = await this.connect(device);
      const healthData = await this.executeChannelCommand(connection, '/system/health/print').catch(() => []);

      if (!healthData || healthData.length === 0) {
        return null;
      }

      const health = healthData[0] || {};

      return {
        voltage: parseFloat(health.voltage) || 0,
        temperature: parseFloat(health.temperature) || 0,
        cpuTemperature: health['cpu-temperature'] ? parseFloat(health['cpu-temperature']) : 0,
        fanSpeed: health['fan-speed'] ? parseInt(health['fan-speed']) : 0,
        powerConsumption: health['power-consumption'] ? parseFloat(health['power-consumption']) : 0,
      };

    } catch (error) {
      logger.error(`Error getting system health for ${device.name}:`, error);
      return null;
    }
  }

  /**
   * Execute custom command
   */
  public async executeCommand(device: MikroTikDevice, command: string, args?: string[]): Promise<any[]> {
    try {
      const connection = await this.connect(device);
      return await this.executeChannelCommand(connection, command, args);
    } catch (error) {
      logger.error(`Error executing command '${command}' on ${device.name}:`, error);
      throw error;
    }
  }

  /**
   * Disconnect all connections
   */
  public async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.connections.entries()).map(
      async ([key, connection]) => {
        try {
          if (connection.connected && connection.connected()) {
            connection.close();
          }
        } catch (error) {
          logger.error(`Error closing connection ${key}:`, error);
        }
      }
    );

    await Promise.all(disconnectPromises);
    this.connections.clear();
    logger.info('All MikroTik connections closed');
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): { [key: string]: boolean } {
    const status: { [key: string]: boolean } = {};

    this.connections.forEach((connection, key) => {
      status[key] = connection.connected ? connection.connected() : false;
    });

    return status;
  }
}

export const mikrotikService = new MikroTikService();
