# 🚀 تشغيل نظام مراقبة المايكروتك
# How to Run MikroTik Monitoring System

## ⚡ التشغيل السريع (الطريقة الموصى بها)

### الخيار 1: استخد<PERSON><PERSON> Docker (الأسهل)

#### المتطلبات:
- Docker Desktop مثبت على النظام
- 4GB RAM على الأقل

#### خطوات التشغيل:

1. **تحميل Docker Desktop**
   - اذهب إلى: https://www.docker.com/products/docker-desktop
   - حمل وثبت Docker Desktop
   - تأكد من تشغيله

2. **تشغيل النظام**
   ```bash
   # في مجلد المشروع
   docker-compose up -d
   ```

3. **الوصول للنظام**
   - افتح المتصفح واذهب إلى: http://localhost:3000
   - استخدم بيانات الدخول:
     - Email: <EMAIL>
     - Password: admin123

---

### الخيار 2: التشغيل اليدوي (للتطوير)

#### المتطلبات:
- Node.js 18+ (من https://nodejs.org)
- PostgreSQL 14+ (من https://www.postgresql.org)
- Redis (من https://redis.io)

#### خطوات التشغيل:

1. **تثبيت قاعدة البيانات**
   ```bash
   # تثبيت PostgreSQL
   # إنشاء قاعدة بيانات جديدة اسمها: mikrotik_monitoring
   
   # تثبيت Redis
   # تشغيل خدمة Redis
   ```

2. **إعداد Backend**
   ```bash
   cd backend
   npm install
   npm run prisma:generate
   npm run prisma:migrate
   npm run dev
   ```

3. **إعداد Frontend (في terminal جديد)**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **الوصول للنظام**
   - Frontend: http://localhost:3000
   - Backend: http://localhost:3001

---

## 🔧 إعداد أجهزة MikroTik

### تفعيل API على جهاز MikroTik

1. **الاتصال بالجهاز**
   - استخدم Winbox أو SSH للاتصال بجهاز MikroTik

2. **تفعيل API**
   ```bash
   /ip service enable api
   ```

3. **إنشاء مستخدم للمراقبة**
   ```bash
   /user group add name=monitoring policy=api,read
   /user add name=monitoring password=monitoring123 group=monitoring
   ```

### إضافة جهاز في النظام

1. سجل دخول للنظام
2. اذهب إلى "الأجهزة" → "إضافة جهاز جديد"
3. أدخل المعلومات:
   - **الاسم**: اسم وصفي للجهاز
   - **عنوان IP**: عنوان IP للجهاز (مثل: ***********)
   - **نوع الجهاز**: Router/Switch/Access Point
   - **اسم المستخدم**: monitoring
   - **كلمة المرور**: monitoring123
   - **منفذ API**: 8728

---

## 🐛 حل المشاكل الشائعة

### مشكلة: لا يمكن الوصول للنظام

**الحل:**
```bash
# فحص حالة الخدمات
docker-compose ps

# إعادة تشغيل
docker-compose restart

# فحص السجلات
docker-compose logs -f
```

### مشكلة: فشل الاتصال بجهاز MikroTik

**الحلول:**
1. تأكد من تفعيل API على الجهاز
2. تحقق من بيانات المصادقة
3. اختبر الاتصال:
   ```bash
   ping ***********
   telnet *********** 8728
   ```

### مشكلة: خطأ في قاعدة البيانات

**الحل:**
```bash
# إعادة تشغيل قاعدة البيانات
docker-compose restart postgres

# فحص السجلات
docker-compose logs postgres
```

---

## 📊 استخدام النظام

### لوحة التحكم الرئيسية
- عرض حالة جميع الأجهزة
- مقاييس الأداء في الوقت الفعلي
- التنبيهات النشطة

### إدارة الأجهزة
- إضافة أجهزة جديدة
- تحرير إعدادات الأجهزة
- مراقبة حالة الأجهزة

### التنبيهات
- عرض جميع التنبيهات
- تأكيد التنبيهات
- إعداد حدود التنبيهات

### الإعدادات
- إعدادات النظام العامة
- إعدادات البريد الإلكتروني
- إدارة المستخدمين

---

## 🔒 الأمان

### إعدادات الأمان الأساسية

1. **غير كلمة مرور المدير**
   - اذهب إلى الإعدادات → الملف الشخصي
   - غير كلمة المرور الافتراضية

2. **استخدم كلمات مرور قوية**
   - لأجهزة MikroTik
   - لحسابات المستخدمين

3. **قيد الوصول للشبكة**
   - استخدم جدار الحماية
   - قيد الوصول للـ API حسب IP

---

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- راجع ملف INSTALLATION.md للتفاصيل الكاملة
- راجع ملف QUICK_START.md للبداية السريعة
- افتح issue في GitHub للمشاكل

### معلومات مفيدة
- **المنافذ المستخدمة**: 
  - 3000 (Frontend)
  - 3001 (Backend)
  - 5432 (PostgreSQL)
  - 6379 (Redis)

- **ملفات الإعدادات**:
  - backend/.env
  - frontend/.env.local

- **السجلات**:
  - Docker: `docker-compose logs`
  - Manual: backend/logs/

---

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

1. ✅ **أضف أجهزة MikroTik الخاصة بك**
2. ✅ **اضبط حدود التنبيهات**
3. ✅ **اعد إعدادات البريد الإلكتروني**
4. ✅ **أنشئ مستخدمين إضافيين**
5. ✅ **اضبط فترات المراقبة**

---

## 🔄 أوامر مفيدة

### Docker Commands
```bash
# تشغيل النظام
docker-compose up -d

# إيقاف النظام
docker-compose down

# مراقبة السجلات
docker-compose logs -f

# إعادة تشغيل خدمة معينة
docker-compose restart backend

# فحص حالة الخدمات
docker-compose ps
```

### Development Commands
```bash
# تشغيل Backend فقط
cd backend && npm run dev

# تشغيل Frontend فقط
cd frontend && npm run dev

# بناء المشروع
npm run build

# تشغيل الاختبارات
npm run test
```
