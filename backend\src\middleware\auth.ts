import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '@/services/database';
import { logger, loggerHelpers } from '@/utils/logger';
import { config } from '@/config/config';
import { UserRole } from '@prisma/client';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    username: string;
    email: string;
    firstName?: string;
    lastName?: string;
    role: UserRole;
    isActive: boolean;
  };
}

/**
 * Authentication middleware
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      loggerHelpers.logAuth('Missing token', undefined, req.ip, false);
      res.status(401).json({
        error: 'Access denied. No token provided.',
        code: 'NO_TOKEN',
      });
      return;
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, config.jwt.secret) as any;
      
      // Get user from database
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
        },
      });

      if (!user) {
        loggerHelpers.logAuth('User not found', decoded.userId, req.ip, false);
        res.status(401).json({
          error: 'Access denied. User not found.',
          code: 'USER_NOT_FOUND',
        });
        return;
      }

      if (!user.isActive) {
        loggerHelpers.logAuth('Inactive user', user.id, req.ip, false);
        res.status(401).json({
          error: 'Access denied. User account is inactive.',
          code: 'USER_INACTIVE',
        });
        return;
      }

      // Attach user to request
      req.user = {
        ...user,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined
      };
      
      // Log successful authentication
      loggerHelpers.logAuth('Token verified', user.id, req.ip, true);
      
      next();

    } catch (jwtError) {
      loggerHelpers.logAuth('Invalid token', undefined, req.ip, false);
      res.status(401).json({
        error: 'Access denied. Invalid token.',
        code: 'INVALID_TOKEN',
      });
      return;
    }

  } catch (error) {
    logger.error('Authentication middleware error:', error);
    res.status(500).json({
      error: 'Internal server error during authentication.',
      code: 'AUTH_ERROR',
    });
  }
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (roles: UserRole | UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'Authentication required.',
        code: 'NOT_AUTHENTICATED',
      });
      return;
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.user.role)) {
      loggerHelpers.logSecurity('Insufficient permissions', req.user.id, req.ip, {
        requiredRoles: allowedRoles,
        userRole: req.user.role,
      });
      
      res.status(403).json({
        error: 'Access denied. Insufficient permissions.',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: allowedRoles,
        current: req.user.role,
      });
      return;
    }

    next();
  };
};

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole('ADMIN');

/**
 * Manager or Admin middleware
 */
export const requireManager = requireRole(['ADMIN', 'MANAGER']);

/**
 * Device ownership middleware
 */
export const requireDeviceAccess = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        error: 'Authentication required.',
        code: 'NOT_AUTHENTICATED',
      });
      return;
    }

    const deviceId = req.params['deviceId'] || req.params['id'];
    
    if (!deviceId) {
      res.status(400).json({
        error: 'Device ID is required.',
        code: 'DEVICE_ID_REQUIRED',
      });
      return;
    }

    // Admin and Manager can access all devices
    if (['ADMIN', 'MANAGER'].includes(req.user.role)) {
      next();
      return;
    }

    // Check if user owns the device
    const device = await prisma.device.findFirst({
      where: {
        id: deviceId,
        ownerId: req.user.id,
      },
    });

    if (!device) {
      loggerHelpers.logSecurity('Device access denied', req.user.id, req.ip, {
        deviceId,
        reason: 'Not owner',
      });
      
      res.status(403).json({
        error: 'Access denied. You do not have permission to access this device.',
        code: 'DEVICE_ACCESS_DENIED',
      });
      return;
    }

    next();

  } catch (error) {
    logger.error('Device access middleware error:', error);
    res.status(500).json({
      error: 'Internal server error during device access check.',
      code: 'DEVICE_ACCESS_ERROR',
    });
  }
};

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      next();
      return;
    }

    try {
      const decoded = jwt.verify(token, config.jwt.secret) as any;
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
        },
      });

      if (user && user.isActive) {
        req.user = {
          ...user,
          firstName: user.firstName || undefined,
          lastName: user.lastName || undefined
        };
      }

    } catch (jwtError) {
      // Ignore JWT errors for optional auth
    }

    next();

  } catch (error) {
    logger.error('Optional auth middleware error:', error);
    next();
  }
};

/**
 * Rate limiting by user
 */
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      next();
      return;
    }

    const userId = req.user.id;
    const now = Date.now();
    const userLimit = userRequests.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize user limit
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      next();
      return;
    }

    if (userLimit.count >= maxRequests) {
      loggerHelpers.logSecurity('Rate limit exceeded', userId, req.ip, {
        maxRequests,
        windowMs,
      });
      
      res.status(429).json({
        error: 'Too many requests. Please try again later.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((userLimit.resetTime - now) / 1000),
      });
      return;
    }

    userLimit.count++;
    next();
  };
};

/**
 * Audit log middleware
 */
export const auditLog = (action: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user || !config.features.enableAuditLogging) {
        next();
        return;
      }

      // Store original end function
      const originalEnd = res.end;
      
      // Override end function to log after response
      res.end = function(chunk?: any, encoding?: any) {
        // Call original end function
        const result = originalEnd.call(this, chunk, encoding);
        
        // Log audit entry
        prisma.auditLog.create({
          data: {
            userId: req.user!.id,
            action,
            resource: req.route?.path || req.path,
            resourceId: req.params['id'] || req.params['deviceId'],
            details: {
              method: req.method,
              url: req.originalUrl,
              statusCode: res.statusCode,
              userAgent: req.get('User-Agent'),
              body: req.method !== 'GET' ? req.body : undefined,
            },
            ipAddress: req.ip,
          },
        }).catch(error => {
          logger.error('Failed to create audit log:', error);
        });

        return result;
      };

      next();

    } catch (error) {
      logger.error('Audit log middleware error:', error);
      next();
    }
  };
};
