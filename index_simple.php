<?php
// تعريف الوصول الآمن
define('SECURE_ACCESS', true);

// بدء الجلسة
session_start();

// التحقق من وجود جلسة مستخدم نشطة
if (isset($_SESSION['user'])) {
    // توجيه المستخدم إلى الصفحة المناسبة بناءً على دوره
    $role = $_SESSION['user']['role'];
    switch ($role) {
        case 'الإدارة العامة':
            header("Location: dashboard/index.php");
            exit();
        case 'مدير':
            header("Location: admin/index.php");
            exit();
        case 'إدارة الجوال':
            header("Location: mobile/index.php");
            exit();
        case 'المخابر':
            header("Location: lab/index.php");
            exit();
        case 'الهندسة':
            header("Location: engineering/index.php");
            exit();
        case 'الصيانة':
            header("Location: maintenance/index.php");
            exit();
    }
}

$site_name = 'الرصين';
$site_description = 'منصة تنظيم التواصل بين وحدات قسم الإتصالات';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($site_name); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($site_description); ?>">
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(120deg, #f8fafc 0%, #e0e7ff 100%);
            font-family: 'Tajawal', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 22px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.13);
            padding: 3rem 2.5rem;
            max-width: 420px;
            width: 100%;
            text-align: center;
        }
        
        .logo-circle {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #6366f1 0%, #0ea5e9 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            font-size: 2.5rem;
            color: white;
        }
        
        .btn-custom {
            background: linear-gradient(90deg, #6366f1 0%, #0ea5e9 100%);
            color: #fff;
            font-weight: bold;
            border: none;
            border-radius: 30px;
            padding: 13px 0;
            width: 100%;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(99,102,241,0.2);
            color: #fff;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature-item {
            background: rgba(99, 102, 241, 0.1);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
        }
        
        .system-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #10b981;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="main-card mx-auto">
        <div class="system-status">
            <div class="status-indicator"></div>
            <span>النظام يعمل بشكل طبيعي</span>
        </div>

        <div class="logo-circle">📡</div>

        <h1 class="mb-3"><?php echo htmlspecialchars($site_name); ?></h1>
        <div class="lead mb-4"><?php echo htmlspecialchars($site_description); ?></div>

        <div class="features-grid">
            <div class="feature-item">
                <div class="mb-2">🔒</div>
                <div>نظام آمن</div>
            </div>
            <div class="feature-item">
                <div class="mb-2">⚡</div>
                <div>سريع وموثوق</div>
            </div>
            <div class="feature-item">
                <div class="mb-2">📱</div>
                <div>متوافق مع الجوال</div>
            </div>
            <div class="feature-item">
                <div class="mb-2">🌐</div>
                <div>واجهة عربية</div>
            </div>
        </div>

        <a href="login.php" class="btn btn-custom mb-3">الدخول إلى النظام</a>
        
        <div class="mt-4">
            <small class="text-muted">
                &copy; <?php echo date("Y"); ?> <?php echo htmlspecialchars($site_name); ?> - جميع الحقوق محفوظة
                <br>الإصدار 2.0 - محدث ومحسن
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
