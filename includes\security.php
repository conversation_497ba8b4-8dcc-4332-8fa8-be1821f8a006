<?php
// ملف الأمان والحماية

// منع الوصول المباشر
if (!defined('SECURE_ACCESS')) {
    die('الوصول المباشر غير مسموح');
}

// دالة تنظيف البيانات
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة التحقق من CSRF Token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة التحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة التحقق من قوة كلمة المرور
function validatePassword($password) {
    // كلمة المرور يجب أن تكون 8 أحرف على الأقل
    if (strlen($password) < 8) {
        return false;
    }
    
    // يجب أن تحتوي على حرف كبير وصغير ورقم
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $password)) {
        return false;
    }
    
    return true;
}

// دالة تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

// دالة التحقق من الجلسة
function checkSession() {
    if (!isset($_SESSION['user'])) {
        header('Location: ../login.php');
        exit();
    }
    
    // التحقق من انتهاء صلاحية الجلسة (4 ساعات)
    if (isset($_SESSION['user']['login_time'])) {
        $session_lifetime = 4 * 60 * 60; // 4 ساعات
        if (time() - $_SESSION['user']['login_time'] > $session_lifetime) {
            session_destroy();
            header('Location: ../login.php?expired=1');
            exit();
        }
    }
}

// دالة التحقق من الصلاحيات
function checkPermission($required_role) {
    checkSession();
    
    if ($_SESSION['user']['role'] !== $required_role) {
        header('Location: ../index.php?error=no_permission');
        exit();
    }
}

// دالة تسجيل العمليات
function logActivity($user_id, $action, $details = '') {
    global $conn;
    
    $ip = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    
    $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, details, ip, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
    $stmt->bind_param("issss", $user_id, $action, $details, $ip, $user_agent);
    $stmt->execute();
    $stmt->close();
}

// دالة منع SQL Injection
function escapeString($conn, $string) {
    return $conn->real_escape_string($string);
}

// دالة التحقق من رفع الملفات
function validateFileUpload($file, $allowed_types = ['jpg', 'jpeg', 'png', 'pdf'], $max_size = 5242880) {
    $errors = [];
    
    // التحقق من وجود خطأ في الرفع
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = 'حدث خطأ أثناء رفع الملف';
        return $errors;
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > $max_size) {
        $errors[] = 'حجم الملف كبير جداً (الحد الأقصى 5 ميجابايت)';
    }
    
    // التحقق من نوع الملف
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        $errors[] = 'نوع الملف غير مدعوم';
    }
    
    // التحقق من نوع MIME
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowed_mimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'pdf' => 'application/pdf'
    ];
    
    if (!in_array($mime_type, $allowed_mimes)) {
        $errors[] = 'نوع الملف غير صحيح';
    }
    
    return $errors;
}

// دالة تنظيف اسم الملف
function sanitizeFileName($filename) {
    // إزالة الأحرف الخطيرة
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    
    // منع الأسماء الخطيرة
    $dangerous_names = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4', 'com5', 'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2', 'lpt3', 'lpt4', 'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9'];
    
    $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);
    if (in_array(strtolower($name_without_ext), $dangerous_names)) {
        $filename = 'safe_' . $filename;
    }
    
    return $filename;
}

// إعدادات الأمان للجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// منع clickjacking
header('X-Frame-Options: DENY');

// منع MIME type sniffing
header('X-Content-Type-Options: nosniff');

// تفعيل XSS protection
header('X-XSS-Protection: 1; mode=block');

// Content Security Policy
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:;");
?>
