import winston from 'winston';
import path from 'path';
import { config } from '@/config/config';

// Create logs directory if it doesn't exist
const logsDir = path.dirname(config.logging.file);

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    return log;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create transports array
const transports: winston.transport[] = [];

// Console transport (always enabled in development)
if (config.nodeEnv === 'development') {
  transports.push(
    new winston.transports.Console({
      level: config.logging.level,
      format: consoleFormat,
    })
  );
}

// File transport
if (config.logging.file) {
  transports.push(
    new winston.transports.File({
      filename: config.logging.file,
      level: config.logging.level,
      format: fileFormat,
      maxsize: parseInt(config.logging.maxSize.replace('m', '')) * 1024 * 1024, // Convert MB to bytes
      maxFiles: config.logging.maxFiles,
      tailable: true,
    })
  );

  // Separate error log file
  transports.push(
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '.error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: parseInt(config.logging.maxSize.replace('m', '')) * 1024 * 1024,
      maxFiles: config.logging.maxFiles,
      tailable: true,
    })
  );
}

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  format: fileFormat,
  transports,
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      format: fileFormat,
    }),
  ],
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      format: fileFormat,
    }),
  ],
  // Exit on handled exceptions
  exitOnError: false,
});

// Add console transport in production for important logs
if (config.nodeEnv === 'production') {
  logger.add(
    new winston.transports.Console({
      level: 'error',
      format: consoleFormat,
    })
  );
}

// Create a stream for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};

// Helper functions for structured logging
export const loggerHelpers = {
  /**
   * Log database query
   */
  logQuery: (query: string, params?: any, duration?: number) => {
    logger.debug('Database Query', {
      query,
      params,
      duration: duration ? `${duration}ms` : undefined,
    });
  },

  /**
   * Log API request
   */
  logRequest: (method: string, url: string, userId?: string, ip?: string) => {
    logger.info('API Request', {
      method,
      url,
      userId,
      ip,
    });
  },

  /**
   * Log API response
   */
  logResponse: (method: string, url: string, statusCode: number, duration: number, userId?: string) => {
    logger.info('API Response', {
      method,
      url,
      statusCode,
      duration: `${duration}ms`,
      userId,
    });
  },

  /**
   * Log authentication event
   */
  logAuth: (event: string, userId?: string, ip?: string, success: boolean = true) => {
    const level = success ? 'info' : 'warn';
    logger.log(level, `Auth: ${event}`, {
      userId,
      ip,
      success,
    });
  },

  /**
   * Log device event
   */
  logDevice: (event: string, deviceId: string, deviceName?: string, details?: any) => {
    logger.info(`Device: ${event}`, {
      deviceId,
      deviceName,
      ...details,
    });
  },

  /**
   * Log alert event
   */
  logAlert: (event: string, alertId: string, deviceId?: string, severity?: string, details?: any) => {
    logger.info(`Alert: ${event}`, {
      alertId,
      deviceId,
      severity,
      ...details,
    });
  },

  /**
   * Log monitoring event
   */
  logMonitoring: (event: string, details?: any) => {
    logger.info(`Monitoring: ${event}`, details);
  },

  /**
   * Log notification event
   */
  logNotification: (event: string, type: string, recipient: string, success: boolean = true, details?: any) => {
    const level = success ? 'info' : 'error';
    logger.log(level, `Notification: ${event}`, {
      type,
      recipient,
      success,
      ...details,
    });
  },

  /**
   * Log security event
   */
  logSecurity: (event: string, userId?: string, ip?: string, details?: any) => {
    logger.warn(`Security: ${event}`, {
      userId,
      ip,
      ...details,
    });
  },

  /**
   * Log performance metric
   */
  logPerformance: (operation: string, duration: number, details?: any) => {
    logger.info(`Performance: ${operation}`, {
      duration: `${duration}ms`,
      ...details,
    });
  },

  /**
   * Log error with context
   */
  logError: (error: Error, context?: string, details?: any) => {
    logger.error(`Error${context ? ` in ${context}` : ''}`, {
      message: error.message,
      stack: error.stack,
      ...details,
    });
  },
};

// Export default logger
export default logger;
