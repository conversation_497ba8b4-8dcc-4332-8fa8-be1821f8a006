# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/mikrotik_monitoring?schema=public"

# Server Configuration
PORT=3001
NODE_ENV=development
API_VERSION=v1

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Monitoring Configuration
MONITORING_INTERVAL=30000
ALERT_CHECK_INTERVAL=60000
CLEANUP_INTERVAL=3600000

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Default Admin User
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_USERNAME=admin

# MikroTik Default Configuration
DEFAULT_API_PORT=8728
DEFAULT_SNMP_PORT=161
DEFAULT_SNMP_COMMUNITY=public

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_TIMEOUT=5000

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=********
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups/

# Performance Configuration
MAX_CONCURRENT_CONNECTIONS=100
CONNECTION_TIMEOUT=30000
QUERY_TIMEOUT=10000

# Feature Flags
ENABLE_SNMP=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_WEBHOOK_NOTIFICATIONS=true
ENABLE_AUDIT_LOGGING=true
ENABLE_METRICS_CLEANUP=true
