import { Router } from 'express';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { prisma } from '@/services/database';
import { logger } from '@/utils/logger';

const router = Router();

// Get system settings
router.get('/', authenticateToken, requireRole(['ADMIN']), async (_req, res) => {
  try {
    const settings = await prisma.setting.findMany({
      orderBy: { key: 'asc' }
    });

    // Convert to key-value object
    const settingsObj = settings.reduce((acc, setting) => {
      acc[setting.key] = {
        value: setting.value,
        description: setting.description,
        type: setting.type,
        updatedAt: setting.updatedAt
      };
      return acc;
    }, {} as any);

    res.json(settingsObj);
  } catch (error) {
    logger.error('Error fetching system settings:', error);
    res.status(500).json({ error: 'Failed to fetch system settings' });
  }
});

// Update system setting
router.put('/:key', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { value, description } = req.body;

    const setting = await prisma.setting.upsert({
      where: { key: req.params['key'] },
      update: {
        value,
        description,
        updatedAt: new Date()
      },
      create: {
        key: req.params['key'],
        value,
        description,
        type: 'STRING'
      }
    });

    res.json(setting);
  } catch (error) {
    logger.error('Error updating system setting:', error);
    res.status(500).json({ error: 'Failed to update system setting' });
  }
});

// Get notification settings
router.get('/notifications', authenticateToken, async (_req, res) => {
  try {
    const settings = await prisma.setting.findMany({
      where: {
        key: {
          startsWith: 'notification_'
        }
      }
    });

    const notificationSettings = settings.reduce((acc, setting) => {
      const key = setting.key.replace('notification_', '');
      acc[key] = setting.value;
      return acc;
    }, {} as any);

    res.json(notificationSettings);
  } catch (error) {
    logger.error('Error fetching notification settings:', error);
    res.status(500).json({ error: 'Failed to fetch notification settings' });
  }
});

// Update notification settings
router.put('/notifications', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const settings = req.body;

    const updatePromises = Object.entries(settings).map(([key, value]) =>
      prisma.setting.upsert({
        where: { key: `notification_${key}` },
        update: {
          value: String(value),
          updatedAt: new Date()
        },
        create: {
          key: `notification_${key}`,
          value: String(value),
          type: 'STRING'
        }
      })
    );

    await Promise.all(updatePromises);

    res.json({ message: 'Notification settings updated successfully' });
  } catch (error) {
    logger.error('Error updating notification settings:', error);
    res.status(500).json({ error: 'Failed to update notification settings' });
  }
});

// Get monitoring settings
router.get('/monitoring', authenticateToken, requireRole(['ADMIN']), async (_req, res) => {
  try {
    const settings = await prisma.setting.findMany({
      where: {
        key: {
          startsWith: 'monitoring_'
        }
      }
    });

    const monitoringSettings = settings.reduce((acc, setting) => {
      const key = setting.key.replace('monitoring_', '');
      acc[key] = setting.value;
      return acc;
    }, {} as any);

    res.json(monitoringSettings);
  } catch (error) {
    logger.error('Error fetching monitoring settings:', error);
    res.status(500).json({ error: 'Failed to fetch monitoring settings' });
  }
});

// Update monitoring settings
router.put('/monitoring', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const settings = req.body;

    const updatePromises = Object.entries(settings).map(([key, value]) =>
      prisma.setting.upsert({
        where: { key: `monitoring_${key}` },
        update: {
          value: String(value),
          updatedAt: new Date()
        },
        create: {
          key: `monitoring_${key}`,
          value: String(value),
          type: 'STRING'
        }
      })
    );

    await Promise.all(updatePromises);

    res.json({ message: 'Monitoring settings updated successfully' });
  } catch (error) {
    logger.error('Error updating monitoring settings:', error);
    res.status(500).json({ error: 'Failed to update monitoring settings' });
  }
});

// Get alert thresholds
router.get('/thresholds', authenticateToken, requireRole(['ADMIN']), async (_req, res) => {
  try {
    const settings = await prisma.setting.findMany({
      where: {
        key: {
          startsWith: 'threshold_'
        }
      }
    });

    const thresholds = settings.reduce((acc, setting) => {
      const key = setting.key.replace('threshold_', '');
      acc[key] = parseFloat(setting.value) || 0;
      return acc;
    }, {} as any);

    res.json(thresholds);
  } catch (error) {
    logger.error('Error fetching alert thresholds:', error);
    res.status(500).json({ error: 'Failed to fetch alert thresholds' });
  }
});

// Update alert thresholds
router.put('/thresholds', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const thresholds = req.body;

    const updatePromises = Object.entries(thresholds).map(([key, value]) =>
      prisma.setting.upsert({
        where: { key: `threshold_${key}` },
        update: {
          value: String(value),
          updatedAt: new Date()
        },
        create: {
          key: `threshold_${key}`,
          value: String(value),
          type: 'NUMBER'
        }
      })
    );

    await Promise.all(updatePromises);

    res.json({ message: 'Alert thresholds updated successfully' });
  } catch (error) {
    logger.error('Error updating alert thresholds:', error);
    res.status(500).json({ error: 'Failed to update alert thresholds' });
  }
});

// Reset settings to defaults
router.post('/reset', authenticateToken, requireRole(['ADMIN']), async (_req, res) => {
  try {
    // Delete all settings (they will be recreated with defaults when needed)
    await prisma.setting.deleteMany({});

    res.json({ message: 'Settings reset to defaults successfully' });
  } catch (error) {
    logger.error('Error resetting settings:', error);
    res.status(500).json({ error: 'Failed to reset settings' });
  }
});

export default router;
