<?php
// معالج الأخطاء المخصص

// منع الوصول المباشر
if (!defined('SECURE_ACCESS')) {
    die('الوصول المباشر غير مسموح');
}

// إعداد معالج الأخطاء المخصص
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');
register_shutdown_function('fatalErrorHandler');

// دالة معالجة الأخطاء
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    // تجاهل الأخطاء المكبوتة بـ @
    if (!(error_reporting() & $errno)) {
        return false;
    }

    $error_types = [
        E_ERROR => 'خطأ فادح',
        E_WARNING => 'تحذير',
        E_PARSE => 'خطأ في التحليل',
        E_NOTICE => 'ملاحظة',
        E_CORE_ERROR => 'خطأ أساسي فادح',
        E_CORE_WARNING => 'تحذير أساسي',
        E_COMPILE_ERROR => 'خطأ في التجميع',
        E_COMPILE_WARNING => 'تحذير في التجميع',
        E_USER_ERROR => 'خطأ من المستخدم',
        E_USER_WARNING => 'تحذير من المستخدم',
        E_USER_NOTICE => 'ملاحظة من المستخدم',
        E_STRICT => 'خطأ صارم',
        E_RECOVERABLE_ERROR => 'خطأ قابل للاسترداد',
        E_DEPRECATED => 'مهجور',
        E_USER_DEPRECATED => 'مهجور من المستخدم'
    ];

    $error_type = $error_types[$errno] ?? 'خطأ غير معروف';
    
    // تسجيل الخطأ
    $log_message = sprintf(
        "[%s] %s: %s في %s على السطر %d",
        date('Y-m-d H:i:s'),
        $error_type,
        $errstr,
        $errfile,
        $errline
    );
    
    error_log($log_message, 3, __DIR__ . '/../logs/error.log');
    
    // في بيئة التطوير، عرض تفاصيل الخطأ
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>{$error_type}:</strong> {$errstr}<br>";
        echo "<strong>الملف:</strong> {$errfile}<br>";
        echo "<strong>السطر:</strong> {$errline}<br>";
        echo "</div>";
    }
    
    // عدم إيقاف تنفيذ السكريبت للأخطاء غير الفادحة
    return true;
}

// دالة معالجة الاستثناءات
function customExceptionHandler($exception) {
    $log_message = sprintf(
        "[%s] استثناء غير معالج: %s في %s على السطر %d\nStack trace:\n%s",
        date('Y-m-d H:i:s'),
        $exception->getMessage(),
        $exception->getFile(),
        $exception->getLine(),
        $exception->getTraceAsString()
    );
    
    error_log($log_message, 3, __DIR__ . '/../logs/error.log');
    
    // في بيئة التطوير، عرض تفاصيل الاستثناء
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>استثناء غير معالج:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>الملف:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>السطر:</strong> " . $exception->getLine() . "<br>";
        echo "<details><summary>تفاصيل إضافية</summary><pre>" . $exception->getTraceAsString() . "</pre></details>";
        echo "</div>";
    } else {
        // في بيئة الإنتاج، عرض رسالة عامة
        showErrorPage('حدث خطأ في النظام، يرجى المحاولة لاحقاً');
    }
}

// دالة معالجة الأخطاء الفادحة
function fatalErrorHandler() {
    $error = error_get_last();
    
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        $log_message = sprintf(
            "[%s] خطأ فادح: %s في %s على السطر %d",
            date('Y-m-d H:i:s'),
            $error['message'],
            $error['file'],
            $error['line']
        );
        
        error_log($log_message, 3, __DIR__ . '/../logs/error.log');
        
        // في بيئة الإنتاج، عرض صفحة خطأ مخصصة
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            showErrorPage('حدث خطأ فادح في النظام');
        }
    }
}

// دالة عرض صفحة الخطأ
function showErrorPage($message = 'حدث خطأ في النظام') {
    // تنظيف أي مخرجات سابقة
    if (ob_get_level()) {
        ob_clean();
    }
    
    http_response_code(500);
    
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>خطأ في النظام - الرصين</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Tajawal', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-container {
                background: white;
                border-radius: 15px;
                padding: 3rem;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                max-width: 500px;
                width: 100%;
            }
            .error-icon {
                font-size: 4rem;
                color: #dc3545;
                margin-bottom: 1rem;
            }
            .error-title {
                color: #343a40;
                font-weight: 700;
                margin-bottom: 1rem;
            }
            .error-message {
                color: #6c757d;
                margin-bottom: 2rem;
            }
            .btn-home {
                background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
                padding: 12px 30px;
                border-radius: 25px;
                text-decoration: none;
                display: inline-block;
                transition: transform 0.2s;
            }
            .btn-home:hover {
                transform: translateY(-2px);
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h2 class="error-title">عذراً، حدث خطأ!</h2>
            <p class="error-message"><?php echo htmlspecialchars($message); ?></p>
            <a href="/" class="btn-home">العودة للصفحة الرئيسية</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// دالة تسجيل الأخطاء المخصصة
function logError($message, $level = 'ERROR') {
    $log_message = sprintf(
        "[%s] [%s] %s",
        date('Y-m-d H:i:s'),
        $level,
        $message
    );
    
    error_log($log_message, 3, __DIR__ . '/../logs/error.log');
}

// دالة تسجيل أخطاء قاعدة البيانات
function logDatabaseError($query, $error, $conn = null) {
    $log_message = sprintf(
        "[%s] [DATABASE ERROR] Query: %s | Error: %s",
        date('Y-m-d H:i:s'),
        $query,
        $error
    );
    
    if ($conn) {
        $log_message .= " | MySQL Error: " . $conn->error;
    }
    
    error_log($log_message, 3, __DIR__ . '/../logs/database.log');
}

// دالة إنشاء مجلد السجلات إذا لم يكن موجوداً
function ensureLogDirectory() {
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // إنشاء ملف .htaccess لحماية مجلد السجلات
    $htaccess_file = $log_dir . '/.htaccess';
    if (!file_exists($htaccess_file)) {
        file_put_contents($htaccess_file, "Deny from all\n");
    }
}

// دالة تنظيف ملفات السجلات القديمة
function cleanupOldLogs($days = 30) {
    $log_dir = __DIR__ . '/../logs';
    $files = glob($log_dir . '/*.log');
    
    foreach ($files as $file) {
        if (filemtime($file) < strtotime("-{$days} days")) {
            unlink($file);
        }
    }
}

// إنشاء مجلد السجلات عند تحميل الملف
ensureLogDirectory();

// دالة معالجة أخطاء AJAX
function handleAjaxError($message, $code = 500) {
    http_response_code($code);
    header('Content-Type: application/json; charset=utf-8');
    
    echo json_encode([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    
    exit;
}

// دالة التحقق من صحة البيانات مع معالجة الأخطاء
function validateData($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;
        
        // التحقق من الحقول المطلوبة
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            $errors[$field] = "الحقل {$field} مطلوب";
            continue;
        }
        
        // التحقق من الحد الأدنى للطول
        if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
            $errors[$field] = "الحقل {$field} يجب أن يكون {$rule['min_length']} أحرف على الأقل";
        }
        
        // التحقق من الحد الأقصى للطول
        if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
            $errors[$field] = "الحقل {$field} يجب أن يكون {$rule['max_length']} أحرف كحد أقصى";
        }
        
        // التحقق من البريد الإلكتروني
        if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $errors[$field] = "الحقل {$field} يجب أن يكون بريد إلكتروني صحيح";
        }
        
        // التحقق من الأرقام
        if (isset($rule['numeric']) && $rule['numeric'] && !is_numeric($value)) {
            $errors[$field] = "الحقل {$field} يجب أن يكون رقماً";
        }
    }
    
    return $errors;
}
?>
