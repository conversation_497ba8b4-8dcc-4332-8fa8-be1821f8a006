<?php
// تعريف الوصول الآمن
define('SECURE_ACCESS', true);

// بدء الجلسة
session_start();

// تضمين ملفات النظام
require_once 'config/db.php';
require_once 'includes/security.php';
require_once 'includes/functions.php';

// التحقق من وجود جلسة مستخدم
if (isset($_SESSION['user'])) {
    $user_id = $_SESSION['user']['id'];
    
    // تسجيل عملية تسجيل الخروج في قاعدة البيانات
    try {
        $stmt = $conn->prepare("UPDATE login_logs SET logout_time = NOW() WHERE user_id = ? AND logout_time IS NULL ORDER BY login_time DESC LIMIT 1");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $stmt->close();
        
        // تسجيل النشاط
        logActivity($user_id, 'logout', 'تسجيل خروج من النظام');
        
    } catch (Exception $e) {
        // تسجيل الخطأ دون إيقاف عملية تسجيل الخروج
        error_log("خطأ في تسجيل عملية الخروج: " . $e->getMessage());
    }
}

// تدمير الجلسة بشكل آمن
$_SESSION = array();

// حذف كوكيز الجلسة
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة إنشاء جلسة جديدة لمنع session fixation
session_start();
session_regenerate_id(true);

// التحقق من وجود معامل redirect
$redirect_url = 'index.php';
if (isset($_GET['redirect']) && !empty($_GET['redirect'])) {
    $redirect = filter_var($_GET['redirect'], FILTER_SANITIZE_URL);
    // التأكد من أن الرابط آمن (داخلي فقط)
    if (strpos($redirect, 'http') !== 0 && strpos($redirect, '//') !== 0) {
        $redirect_url = $redirect;
    }
}

// رسالة تأكيد تسجيل الخروج
$logout_message = isset($_GET['message']) ? htmlspecialchars($_GET['message']) : 'تم تسجيل الخروج بنجاح';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الخروج - الرصين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #0dcaf0 0%, #6610f2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .logout-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .logout-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1.5rem;
            animation: checkmark 0.6s ease-in-out;
        }
        
        @keyframes checkmark {
            0% {
                transform: scale(0);
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
            }
        }
        
        .logout-title {
            color: #343a40;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .logout-message {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(90deg, #0dcaf0 0%, #6610f2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 600;
            margin: 0.5rem;
        }
        
        .btn-home:hover {
            transform: translateY(-2px) scale(1.05);
            color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-login {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 600;
            margin: 0.5rem;
        }
        
        .btn-login:hover {
            transform: translateY(-2px) scale(1.05);
            color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .security-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 1rem;
            margin: 2rem 0;
            font-size: 0.9rem;
            color: #0066cc;
        }
        
        .countdown {
            font-size: 1.2rem;
            font-weight: 600;
            color: #6610f2;
            margin: 1rem 0;
        }
        
        .footer-info {
            margin-top: 2rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .logout-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .logout-title {
                font-size: 1.8rem;
            }
            
            .logout-icon {
                font-size: 3rem;
            }
            
            .btn-home, .btn-login {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-icon">✅</div>
        <h1 class="logout-title">تم تسجيل الخروج بنجاح</h1>
        <p class="logout-message"><?php echo $logout_message; ?></p>
        
        <div class="security-info">
            <strong>🔒 معلومة أمنية:</strong>
            <br>
            تم إنهاء جلستك بشكل آمن. لحماية حسابك، تأكد من إغلاق المتصفح إذا كنت تستخدم جهاز مشترك.
        </div>
        
        <div class="countdown" id="countdown">
            سيتم توجيهك تلقائياً خلال <span id="timer">10</span> ثوانٍ
        </div>
        
        <div class="action-buttons">
            <a href="<?php echo htmlspecialchars($redirect_url); ?>" class="btn-home">العودة للصفحة الرئيسية</a>
            <a href="login.php" class="btn-login">تسجيل دخول جديد</a>
        </div>
        
        <div class="footer-info">
            <small>
                وقت تسجيل الخروج: <?php echo formatArabicDate(date('Y-m-d H:i:s')); ?>
                <br>
                © <?php echo date('Y'); ?> الرصين - جميع الحقوق محفوظة
            </small>
        </div>
    </div>
    
    <script>
        // العد التنازلي للتوجيه التلقائي
        let countdown = 10;
        const timerElement = document.getElementById('timer');
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(function() {
            countdown--;
            timerElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                countdownElement.innerHTML = 'جاري التوجيه...';
                window.location.href = '<?php echo htmlspecialchars($redirect_url); ?>';
            }
        }, 1000);
        
        // إيقاف العد التنازلي عند النقر على أي رابط
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function() {
                clearInterval(timer);
                countdownElement.style.display = 'none';
            });
        });
        
        // إضافة تأثيرات تفاعلية للأزرار
        document.querySelectorAll('.btn-home, .btn-login').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.05)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // منع الرجوع للصفحة السابقة
        history.pushState(null, null, location.href);
        window.onpopstate = function () {
            history.go(1);
        };
        
        // تنظيف البيانات المحلية
        if (typeof(Storage) !== "undefined") {
            localStorage.removeItem('user_session');
            sessionStorage.clear();
        }
    </script>
</body>
</html>
