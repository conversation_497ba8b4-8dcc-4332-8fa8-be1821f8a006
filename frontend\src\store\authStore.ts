import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User, AuthTokens, LoginCredentials, RegisterData } from '@/types';
import { config } from '@/config/config';
import { apiService } from '@/services/api';

interface AuthState {
  // State
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  setTokens: (accessToken: string, refreshToken: string) => void;
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
}

export const authStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiService.post('/auth/login', credentials);
          
          const { user, tokens } = response;
          const { accessToken, refreshToken } = tokens;

          // Set tokens in API service
          apiService.setAuthToken(accessToken);

          // Update state
          set({
            user,
            token: accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Login failed',
            isAuthenticated: false,
            user: null,
            token: null,
            refreshToken: null,
          });
          throw error;
        }
      },

      // Register action
      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiService.post('/auth/register', data);
          
          const { user, tokens } = response;
          const { accessToken, refreshToken } = tokens;

          // Set tokens in API service
          apiService.setAuthToken(accessToken);

          // Update state
          set({
            user,
            token: accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Registration failed',
            isAuthenticated: false,
            user: null,
            token: null,
            refreshToken: null,
          });
          throw error;
        }
      },

      // Logout action
      logout: () => {
        // Remove token from API service
        apiService.removeAuthToken();

        // Clear state
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });

        // Redirect to login page
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      },

      // Refresh authentication
      refreshAuth: async () => {
        try {
          const { refreshToken } = get();
          
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          const response = await apiService.post('/auth/refresh', { refreshToken });
          const { accessToken, refreshToken: newRefreshToken } = response.tokens;

          // Set new token in API service
          apiService.setAuthToken(accessToken);

          // Update state
          set({
            token: accessToken,
            refreshToken: newRefreshToken,
          });

        } catch (error: any) {
          console.error('Token refresh failed:', error);
          get().logout();
          throw error;
        }
      },

      // Update profile
      updateProfile: async (data: Partial<User>) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiService.put('/auth/profile', data);
          const { user } = response;

          set({
            user,
            isLoading: false,
            error: null,
          });

        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Profile update failed',
          });
          throw error;
        }
      },

      // Change password
      changePassword: async (currentPassword: string, newPassword: string) => {
        try {
          set({ isLoading: true, error: null });

          await apiService.put('/auth/change-password', {
            currentPassword,
            newPassword,
          });

          set({
            isLoading: false,
            error: null,
          });

        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Password change failed',
          });
          throw error;
        }
      },

      // Set tokens
      setTokens: (accessToken: string, refreshToken: string) => {
        apiService.setAuthToken(accessToken);
        set({
          token: accessToken,
          refreshToken,
          isAuthenticated: true,
        });
      },

      // Set user
      setUser: (user: User) => {
        set({ user });
      },

      // Set loading
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Set error
      setError: (error: string | null) => {
        set({ error });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Check authentication status
      checkAuthStatus: async () => {
        try {
          const { token } = get();
          
          if (!token) {
            return;
          }

          // Set token in API service
          apiService.setAuthToken(token);

          // Verify token by fetching user profile
          const response = await apiService.get('/auth/me');
          const { user } = response;

          set({
            user,
            isAuthenticated: true,
          });

        } catch (error: any) {
          console.error('Auth status check failed:', error);
          get().logout();
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors
export const useAuth = () => authStore((state) => ({
  user: state.user,
  isAuthenticated: state.isAuthenticated,
  isLoading: state.isLoading,
  error: state.error,
}));

export const useAuthActions = () => authStore((state) => ({
  login: state.login,
  register: state.register,
  logout: state.logout,
  updateProfile: state.updateProfile,
  changePassword: state.changePassword,
  clearError: state.clearError,
}));

// Helper functions
export const isUserRole = (requiredRoles: string | string[]): boolean => {
  const { user } = authStore.getState();
  if (!user) return false;

  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  return roles.includes(user.role);
};

export const hasPermission = (permission: string): boolean => {
  const { user } = authStore.getState();
  if (!user) return false;

  // Define role hierarchy
  const roleHierarchy: Record<string, number> = {
    VIEWER: 1,
    USER: 2,
    MANAGER: 3,
    ADMIN: 4,
  };

  // Define permission requirements
  const permissionRequirements: Record<string, number> = {
    'view:dashboard': 1,
    'view:devices': 1,
    'view:alerts': 1,
    'create:device': 2,
    'edit:device': 2,
    'delete:device': 2,
    'manage:alerts': 2,
    'view:users': 3,
    'manage:users': 3,
    'view:settings': 3,
    'manage:settings': 4,
    'view:audit': 4,
  };

  const userLevel = roleHierarchy[user.role] || 0;
  const requiredLevel = permissionRequirements[permission] || 999;

  return userLevel >= requiredLevel;
};

export const getAuthToken = (): string | null => {
  return authStore.getState().token;
};

export const getRefreshToken = (): string | null => {
  return authStore.getState().refreshToken;
};

export const getCurrentUser = (): User | null => {
  return authStore.getState().user;
};

// Initialize auth on app start
if (typeof window !== 'undefined') {
  // Check auth status on page load
  authStore.getState().checkAuthStatus();

  // Set up token refresh timer
  const setupTokenRefresh = () => {
    const { token, refreshAuth } = authStore.getState();
    
    if (token) {
      // Refresh token every 50 minutes (assuming 1 hour expiry)
      const refreshInterval = 50 * 60 * 1000;
      
      setInterval(() => {
        refreshAuth().catch(() => {
          // Refresh failed, user will be logged out
        });
      }, refreshInterval);
    }
  };

  setupTokenRefresh();
}

export default authStore;
