# دليل التشغيل السريع
# Quick Start Guide

## 🚀 التشغيل السريع باستخدام Docker (الطريقة الموصى بها)

### المتطلبات
- Docker و Docker Compose مثبتان على النظام
- 4GB RAM على الأقل
- 10GB مساحة فارغة

### خطوات التشغيل

```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd mikrotik-monitoring

# 2. تشغيل النظام
docker-compose up -d

# 3. مراقبة السجلات
docker-compose logs -f
```

### الوصول للنظام
- **الواجهة الأمامية**: http://localhost:3000
- **API**: http://localhost:3001
- **بيانات الدخول الافتراضية**:
  - Email: <EMAIL>
  - Password: admin123

---

## 💻 التشغيل اليدوي (للتطوير)

### المتطلبات
- Node.js 18+
- PostgreSQL 14+
- Redis 6+

### 1. إعداد قاعدة البيانات

```bash
# تثبيت PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# إنشاء قاعدة البيانات
sudo -u postgres createdb mikrotik_monitoring
sudo -u postgres psql -c "CREATE USER postgres WITH PASSWORD 'postgres123';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mikrotik_monitoring TO postgres;"

# تثبيت Redis
sudo apt install redis-server
sudo systemctl start redis-server
```

### 2. إعداد Backend

```bash
cd backend

# تثبيت المكتبات
npm install

# إعداد قاعدة البيانات
npm run prisma:generate
npm run prisma:migrate

# تشغيل في وضع التطوير
npm run dev
```

### 3. إعداد Frontend

```bash
# في terminal جديد
cd frontend

# تثبيت المكتبات
npm install

# تشغيل في وضع التطوير
npm run dev
```

### 4. الوصول للنظام
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

---

## 🔧 إعداد أجهزة MikroTik

### تفعيل API على جهاز MikroTik

```bash
# الاتصال بجهاز MikroTik عبر SSH أو Winbox
/ip service enable api

# إنشاء مستخدم للمراقبة
/user group add name=monitoring policy=api,read
/user add name=monitoring password=monitoring123 group=monitoring
```

### إضافة جهاز في النظام

1. سجل دخول للنظام
2. اذهب إلى "الأجهزة" → "إضافة جهاز جديد"
3. أدخل المعلومات:
   - **الاسم**: اسم وصفي للجهاز
   - **عنوان IP**: *********** (مثال)
   - **نوع الجهاز**: Router/Switch/Access Point
   - **اسم المستخدم**: monitoring
   - **كلمة المرور**: monitoring123
   - **منفذ API**: 8728

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```bash
# فحص حالة PostgreSQL
sudo systemctl status postgresql

# إعادة تشغيل
sudo systemctl restart postgresql
```

#### خطأ في الاتصال بـ Redis
```bash
# فحص حالة Redis
sudo systemctl status redis-server

# إعادة تشغيل
sudo systemctl restart redis-server
```

#### فشل الاتصال بجهاز MikroTik
1. تأكد من تفعيل API على الجهاز
2. تحقق من بيانات المصادقة
3. اختبر الاتصال:
```bash
ping ***********
telnet *********** 8728
```

### فحص السجلات

```bash
# Docker
docker-compose logs backend
docker-compose logs frontend

# التشغيل اليدوي
# سجلات Backend في: backend/logs/
# سجلات Frontend في console المتصفح
```

---

## 📊 الميزات الرئيسية

### لوحة التحكم
- عرض حالة جميع الأجهزة
- مقاييس الأداء في الوقت الفعلي
- رسوم بيانية تفاعلية

### المراقبة
- مراقبة CPU والذاكرة
- مراقبة حركة البيانات
- مراقبة درجة الحرارة
- مراقبة حالة الواجهات

### التنبيهات
- تنبيهات فورية عند المشاكل
- إشعارات عبر البريد الإلكتروني
- مستويات خطورة مختلفة
- إدارة التنبيهات

### إدارة الأجهزة
- إضافة/تحرير/حذف الأجهزة
- تجميع الأجهزة
- إعدادات مخصصة لكل جهاز

---

## 🔒 الأمان

### إعدادات الأمان الأساسية

1. **غير كلمة مرور المدير الافتراضية**
2. **استخدم كلمات مرور قوية لأجهزة MikroTik**
3. **قيد الوصول للـ API حسب IP**
4. **فعل HTTPS في الإنتاج**

### إعداد HTTPS

```bash
# إنشاء شهادة SSL
mkdir ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem
```

---

## 📞 الدعم

### الحصول على المساعدة
- راجع ملف INSTALLATION.md للتفاصيل الكاملة
- افتح issue في GitHub للمشاكل
- راجع السجلات للأخطاء

### معلومات مفيدة
- **المنافذ المستخدمة**: 3000 (Frontend), 3001 (Backend), 5432 (PostgreSQL), 6379 (Redis)
- **ملفات الإعدادات**: backend/.env, frontend/.env.local
- **السجلات**: backend/logs/, docker logs

---

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

1. **أضف أجهزة MikroTik الخاصة بك**
2. **اضبط حدود التنبيهات**
3. **اعد إعدادات البريد الإلكتروني**
4. **أنشئ مستخدمين إضافيين**
5. **اضبط فترات المراقبة**

---

## 📈 مراقبة الأداء

### مقاييس مهمة للمراقبة
- استخدام CPU: < 80%
- استخدام الذاكرة: < 85%
- درجة الحرارة: < 70°C
- حالة الواجهات: UP
- زمن الاستجابة: < 100ms

### تحسين الأداء
- قلل فترة المراقبة للأجهزة المهمة
- استخدم فلاتر للتنبيهات
- نظف البيانات القديمة بانتظام
