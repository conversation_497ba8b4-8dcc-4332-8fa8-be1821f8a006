<?php
session_start();

// تنظيف جميع بيانات الجلسة
$_SESSION = array();

// حذف ملف تعريف الارتباط للجلسة إذا كان موجوداً
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header("Location: login.php?logout=success");
exit();
?>
