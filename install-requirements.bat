@echo off
echo 📦 MikroTik Monitoring System - Requirements Installer
echo ====================================================
echo.

echo This script will help you install the required software.
echo Please run this script as Administrator for best results.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Warning: Not running as Administrator
    echo Some installations might fail. Please run as Administrator if needed.
    echo.
)

echo Choose what to install:
echo 1) Install Node.js (Required)
echo 2) Install Docker Desktop (Recommended)
echo 3) Install both Node.js and Docker
echo 4) Check current installations
echo 5) Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto install_nodejs
if "%choice%"=="2" goto install_docker
if "%choice%"=="3" goto install_both
if "%choice%"=="4" goto check_installations
if "%choice%"=="5" goto exit_script
echo Invalid choice. Please run the script again.
pause
exit /b 1

:install_nodejs
echo.
echo 📥 Installing Node.js...
echo.
echo Opening Node.js download page...
echo Please download and install Node.js LTS version.
echo.
start https://nodejs.org/
echo.
echo After installation:
echo 1. Close this window
echo 2. Open a new Command Prompt
echo 3. Run: node --version
echo 4. Run: npm --version
echo.
echo Press any key when installation is complete...
pause >nul
goto check_nodejs

:install_docker
echo.
echo 🐳 Installing Docker Desktop...
echo.
echo Opening Docker Desktop download page...
echo Please download and install Docker Desktop for Windows.
echo.
start https://www.docker.com/products/docker-desktop
echo.
echo After installation:
echo 1. Restart your computer
echo 2. Start Docker Desktop
echo 3. Wait for Docker to start completely
echo 4. Open a new Command Prompt
echo 5. Run: docker --version
echo.
echo Press any key when installation is complete...
pause >nul
goto check_docker

:install_both
echo.
echo 📦 Installing both Node.js and Docker Desktop...
echo.
echo Opening download pages...
start https://nodejs.org/
timeout /t 2 /nobreak >nul
start https://www.docker.com/products/docker-desktop
echo.
echo Please install both applications:
echo 1. Install Node.js first
echo 2. Install Docker Desktop second
echo 3. Restart your computer after Docker installation
echo 4. Start Docker Desktop
echo.
echo Press any key when both installations are complete...
pause >nul
goto check_installations

:check_installations
echo.
echo 🔍 Checking current installations...
echo.

REM Check Node.js
echo Checking Node.js...
where node >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js is installed
    node --version
    npm --version
) else (
    echo ❌ Node.js is NOT installed
)
echo.

REM Check Docker
echo Checking Docker...
where docker >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ Docker is installed
    docker --version
    
    REM Check if Docker is running
    docker info >nul 2>nul
    if %errorlevel% equ 0 (
        echo ✅ Docker is running
    ) else (
        echo ⚠️  Docker is installed but not running
        echo Please start Docker Desktop
    )
) else (
    echo ❌ Docker is NOT installed
)
echo.

REM Check Docker Compose
where docker-compose >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ Docker Compose is installed
    docker-compose --version
) else (
    echo ❌ Docker Compose is NOT installed
)
echo.

echo 📋 Installation Status Summary:
echo ================================
echo.

where node >nul 2>nul
if %errorlevel% equ 0 (
    where docker >nul 2>nul
    if %errorlevel% equ 0 (
        echo ✅ All requirements are installed!
        echo You can now run the MikroTik Monitoring System.
        echo.
        echo Next steps:
        echo 1. Run: start.bat (for Docker)
        echo 2. Or run: dev-start.bat (for manual development)
    ) else (
        echo ✅ Node.js is installed
        echo ❌ Docker is missing (optional but recommended)
        echo.
        echo You can run in development mode with: dev-start.bat
        echo Or install Docker for easier deployment.
    )
) else (
    echo ❌ Node.js is missing (required)
    echo Please install Node.js first.
)
echo.
goto menu

:check_nodejs
echo.
echo Checking Node.js installation...
where node >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js installed successfully!
    node --version
    npm --version
) else (
    echo ❌ Node.js installation failed or not in PATH
    echo Please try installing again or restart Command Prompt
)
echo.
goto menu

:check_docker
echo.
echo Checking Docker installation...
where docker >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ Docker installed successfully!
    docker --version
    
    docker info >nul 2>nul
    if %errorlevel% equ 0 (
        echo ✅ Docker is running
    ) else (
        echo ⚠️  Docker is installed but not running
        echo Please start Docker Desktop
    )
) else (
    echo ❌ Docker installation failed or not in PATH
    echo Please try installing again or restart your computer
)
echo.
goto menu

:menu
echo.
echo What would you like to do next?
echo 1) Check installations again
echo 2) Install more software
echo 3) Run MikroTik Monitoring System
echo 4) Exit
echo.
set /p next_choice="Enter your choice (1-4): "

if "%next_choice%"=="1" goto check_installations
if "%next_choice%"=="2" goto install_requirements.bat
if "%next_choice%"=="3" goto run_system
if "%next_choice%"=="4" goto exit_script
echo Invalid choice.
goto menu

:run_system
echo.
echo 🚀 Starting MikroTik Monitoring System...
echo.

where docker >nul 2>nul
if %errorlevel% equ 0 (
    echo Docker is available. Starting with Docker...
    start.bat
) else (
    where node >nul 2>nul
    if %errorlevel% equ 0 (
        echo Node.js is available. Starting in development mode...
        dev-start.bat
    ) else (
        echo ❌ Neither Docker nor Node.js is available
        echo Please install the requirements first.
        pause
    )
)
goto :eof

:exit_script
echo.
echo 👋 Thank you for using MikroTik Monitoring System installer!
echo.
echo If you installed new software, please:
echo 1. Restart Command Prompt
echo 2. Run the system with start.bat or dev-start.bat
echo.
pause
exit /b 0
